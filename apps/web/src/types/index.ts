import { <PERSON><PERSON>tate, <PERSON><PERSON><PERSON>, Card, <PERSON>Phase, PlayerAction } from '@blackjack/game-core';

/**
 * UI-specific player state with additional display properties
 */
export interface UIPlayerState extends PlayerState {
  /**
   * Position on the table (for layout)
   */
  position: {
    x: number;
    y: number;
    rotation: number;
  };
  /**
   * Whether this is the local player
   */
  isLocalPlayer: boolean;
}

/**
 * Game UI state
 */
export interface GameUIState {
  /**
   * Current table state
   */
  tableState: TableState | null;
  /**
   * Local player's user ID
   */
  localPlayerId: string | null;
  /**
   * Whether the game is loading
   */
  isLoading: boolean;
  /**
   * Current error message
   */
  error: string | null;
  /**
   * Whether the client is connected
   */
  isConnected: boolean;
  /**
   * Current balance
   */
  balance: number;
  /**
   * Whether betting panel is visible
   */
  showBettingPanel: boolean;
  /**
   * Whether action panel is visible
   */
  showActionPanel: boolean;
  /**
   * Available actions for the local player
   */
  availableActions: PlayerAction[];
}

/**
 * Audio preferences
 */
export interface AudioPreferences {
  /**
   * Whether music is muted
   */
  isMuted: boolean;
  /**
   * Volume level (0-1)
   */
  volume: number;
}

/**
 * Toast notification
 */
export interface ToastNotification {
  /**
   * Unique ID
   */
  id: string;
  /**
   * Toast type
   */
  type: 'success' | 'error' | 'warning' | 'info';
  /**
   * Message to display
   */
  message: string;
  /**
   * Duration in milliseconds (0 = permanent)
   */
  duration: number;
  /**
   * When the toast was created
   */
  timestamp: Date;
}

/**
 * Card animation state
 */
export interface CardAnimation {
  /**
   * Card being animated
   */
  card: Card;
  /**
   * Start position
   */
  from: { x: number; y: number };
  /**
   * End position
   */
  to: { x: number; y: number };
  /**
   * Animation duration in ms
   */
  duration: number;
  /**
   * Animation start time
   */
  startTime: number;
  /**
   * Whether the card should flip during animation
   */
  shouldFlip: boolean;
}

/**
 * Timer display props
 */
export interface TimerProps {
  /**
   * Time remaining in milliseconds
   */
  timeLeft: number;
  /**
   * Total time for this phase
   */
  totalTime: number;
  /**
   * Timer size in pixels
   */
  size?: number;
  /**
   * Whether to show the timer
   */
  visible?: boolean;
  /**
   * Color theme
   */
  color?: 'primary' | 'warning' | 'danger';
}

/**
 * Betting chip props
 */
export interface ChipProps {
  /**
   * Chip value
   */
  value: number;
  /**
   * Whether the chip is selected
   */
  selected?: boolean;
  /**
   * Whether the chip is disabled
   */
  disabled?: boolean;
  /**
   * Click handler
   */
  onClick?: () => void;
  /**
   * Size variant
   */
  size?: 'small' | 'medium' | 'large';
}

/**
 * Card component props
 */
export interface CardProps {
  /**
   * Card to display
   */
  card: Card;
  /**
   * Whether to show card back
   */
  faceDown?: boolean;
  /**
   * Size variant
   */
  size?: 'small' | 'medium' | 'large';
  /**
   * Animation state
   */
  animation?: CardAnimation;
  /**
   * Click handler
   */
  onClick?: () => void;
  /**
   * Additional CSS classes
   */
  className?: string;
}

/**
 * Hand component props
 */
export interface HandProps {
  /**
   * Cards in the hand
   */
  cards: Card[];
  /**
   * Hand value
   */
  value: number;
  /**
   * Whether the hand is soft
   */
  isSoft: boolean;
  /**
   * Whether the hand is busted
   */
  isBusted: boolean;
  /**
   * Whether the hand is blackjack
   */
  isBlackjack: boolean;
  /**
   * Whether to show hand value
   */
  showValue?: boolean;
  /**
   * Size variant
   */
  size?: 'small' | 'medium' | 'large';
  /**
   * Layout orientation
   */
  orientation?: 'horizontal' | 'vertical';
  /**
   * Additional CSS classes
   */
  className?: string;
}

/**
 * Player seat props
 */
export interface PlayerSeatProps {
  /**
   * Player state
   */
  player: UIPlayerState | null;
  /**
   * Seat index (0-7)
   */
  seatIndex: number;
  /**
   * Whether this seat is active
   */
  isActive: boolean;
  /**
   * Whether this is the local player's seat
   */
  isLocalPlayer: boolean;
  /**
   * Click handler for empty seat
   */
  onSeatClick?: () => void;
}

/**
 * Action button props
 */
export interface ActionButtonProps {
  /**
   * Button action
   */
  action: PlayerAction;
  /**
   * Whether the button is disabled
   */
  disabled?: boolean;
  /**
   * Click handler
   */
  onClick: () => void;
  /**
   * Keyboard shortcut
   */
  shortcut?: string;
  /**
   * Size variant
   */
  size?: 'small' | 'medium' | 'large';
}

/**
 * Leaderboard entry for display
 */
export interface LeaderboardEntry {
  rank: number;
  displayName: string;
  netWinnings: number;
  handsPlayed: number;
  isCurrentPlayer?: boolean;
}
