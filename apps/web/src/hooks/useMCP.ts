import { useState, useEffect, useCallback } from 'react';
import { mcpClient } from '../services/mcpClient';

export interface MCPState {
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
}

export interface MCPActions {
  connect: () => Promise<void>;
  disconnect: () => Promise<void>;
  sendMessage: (message: string) => Promise<string>;
}

export function useMCP(): MCPState & MCPActions {
  const [state, setState] = useState<MCPState>({
    isConnected: false,
    isConnecting: false,
    error: null
  });

  const connect = useCallback(async () => {
    setState(prev => ({ ...prev, isConnecting: true, error: null }));
    
    try {
      await mcpClient.connect();
      setState(prev => ({ 
        ...prev, 
        isConnected: true, 
        isConnecting: false 
      }));
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        isConnecting: false, 
        error: error instanceof Error ? error.message : 'Connection failed' 
      }));
    }
  }, []);

  const disconnect = useCallback(async () => {
    try {
      await mcpClient.disconnect();
      setState(prev => ({ 
        ...prev, 
        isConnected: false, 
        error: null 
      }));
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Disconnection failed' 
      }));
    }
  }, []);

  const sendMessage = useCallback(async (message: string): Promise<string> => {
    if (!state.isConnected) {
      throw new Error('Not connected to MCP server');
    }

    try {
      const response = await mcpClient.sendMessage(message);
      return response;
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Message sending failed' 
      }));
      throw error;
    }
  }, [state.isConnected]);

  // Check connection status on mount
  useEffect(() => {
    const checkConnection = () => {
      const connected = mcpClient.isClientConnected();
      setState(prev => ({ ...prev, isConnected: connected }));
    };

    checkConnection();
  }, []);

  return {
    ...state,
    connect,
    disconnect,
    sendMessage
  };
}
