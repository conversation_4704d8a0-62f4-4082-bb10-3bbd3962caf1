import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { BlackjackClient, ClientEventType, ConnectionState } from '@blackjack/client-sdk';
import { TableState, PlayerAction, GamePhase, getValidActions } from '@blackjack/game-core';
import { GameUIState, ToastNotification } from '../types/index.js';

/**
 * Game context state
 */
interface GameContextState extends GameUIState {
  client: BlackjackClient | null;
  toasts: ToastNotification[];
}

/**
 * Game context actions
 */
type GameAction = 
  | { type: 'SET_CLIENT'; payload: BlackjackClient }
  | { type: 'SET_CONNECTION_STATE'; payload: boolean }
  | { type: 'SET_TABLE_STATE'; payload: TableState }
  | { type: 'SET_LOCAL_PLAYER_ID'; payload: string }
  | { type: 'SET_BALANCE'; payload: number }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'ADD_TOAST'; payload: ToastNotification }
  | { type: 'REMOVE_TOAST'; payload: string }
  | { type: 'UPDATE_UI_STATE' };

/**
 * Initial state
 */
const initialState: GameContextState = {
  client: null,
  tableState: null,
  localPlayerId: null,
  isLoading: false,
  error: null,
  isConnected: false,
  balance: 0,
  showBettingPanel: false,
  showActionPanel: false,
  availableActions: [],
  toasts: []
};

/**
 * Game reducer
 */
function gameReducer(state: GameContextState, action: GameAction): GameContextState {
  switch (action.type) {
    case 'SET_CLIENT':
      return { ...state, client: action.payload };
    
    case 'SET_CONNECTION_STATE':
      return { ...state, isConnected: action.payload };
    
    case 'SET_TABLE_STATE':
      return { 
        ...state, 
        tableState: action.payload,
        ...updateUIStateFromTable(state, action.payload)
      };
    
    case 'SET_LOCAL_PLAYER_ID':
      return { ...state, localPlayerId: action.payload };
    
    case 'SET_BALANCE':
      return { ...state, balance: action.payload };
    
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    
    case 'ADD_TOAST':
      return { 
        ...state, 
        toasts: [...state.toasts, action.payload]
      };
    
    case 'REMOVE_TOAST':
      return { 
        ...state, 
        toasts: state.toasts.filter(toast => toast.id !== action.payload)
      };
    
    case 'UPDATE_UI_STATE':
      return {
        ...state,
        ...updateUIStateFromTable(state, state.tableState)
      };
    
    default:
      return state;
  }
}

/**
 * Update UI state based on table state
 */
function updateUIStateFromTable(state: GameContextState, tableState: TableState | null): Partial<GameContextState> {
  if (!tableState || !state.localPlayerId) {
    return {
      showBettingPanel: false,
      showActionPanel: false,
      availableActions: []
    };
  }

  const localPlayer = tableState.players.find(p => p.userId === state.localPlayerId);
  if (!localPlayer) {
    return {
      showBettingPanel: false,
      showActionPanel: false,
      availableActions: []
    };
  }

  const showBettingPanel = tableState.phase === GamePhase.BETTING && localPlayer.currentBet === 0;
  const showActionPanel = tableState.phase === GamePhase.PLAYER_TURNS && localPlayer.isActive;
  const availableActions = showActionPanel ? getValidActions(localPlayer.hand) : [];

  return {
    showBettingPanel,
    showActionPanel,
    availableActions
  };
}

/**
 * Game context
 */
const GameContext = createContext<{
  state: GameContextState;
  dispatch: React.Dispatch<GameAction>;
  actions: {
    initializeClient: (config: any) => Promise<void>;
    authenticateDevice: (deviceId: string) => Promise<void>;
    joinTable: (tableId?: string) => Promise<void>;
    leaveTable: () => Promise<void>;
    placeBet: (amount: number) => Promise<void>;
    hit: () => Promise<void>;
    stand: () => Promise<void>;
    double: () => Promise<void>;
    addToast: (type: ToastNotification['type'], message: string, duration?: number) => void;
    removeToast: (id: string) => void;
  };
} | null>(null);

/**
 * Game context provider
 */
export function GameProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(gameReducer, initialState);

  /**
   * Initialize the client
   */
  const initializeClient = async (config: any) => {
    const client = new BlackjackClient(config);
    dispatch({ type: 'SET_CLIENT', payload: client });

    // Set up event listeners
    client.on(ClientEventType.CONNECTION_STATE_CHANGED, (event) => {
      dispatch({ type: 'SET_CONNECTION_STATE', payload: event.state === ConnectionState.CONNECTED });
    });

    client.on(ClientEventType.AUTHENTICATED, (event) => {
      dispatch({ type: 'SET_LOCAL_PLAYER_ID', payload: event.userId });
    });

    client.on(ClientEventType.TABLE_STATE_UPDATED, (event) => {
      dispatch({ type: 'SET_TABLE_STATE', payload: event.tableState });
    });

    client.on(ClientEventType.WALLET_UPDATED, (event) => {
      dispatch({ type: 'SET_BALANCE', payload: event.chips });
      
      // Show toast for wallet updates
      if (event.metadata.type === 'faucet') {
        addToast('info', `+${event.metadata.amount} chips (faucet)`, 3000);
      } else if (event.metadata.type === 'bet_payout') {
        const payout = event.metadata.payout;
        if (payout > 0) {
          addToast('success', `+${payout} chips won!`, 3000);
        } else if (payout < 0) {
          addToast('error', `${Math.abs(payout)} chips lost`, 3000);
        } else {
          addToast('warning', 'Push - bet returned', 3000);
        }
      }
    });

    client.on(ClientEventType.ERROR, (event) => {
      addToast('error', event.message, 5000);
    });

    client.on(ClientEventType.ROUND_STARTED, (event) => {
      addToast('info', `Round ${event.roundId} started`, 2000);
    });
  };

  /**
   * Authenticate with device ID
   */
  const authenticateDevice = async (deviceId: string) => {
    if (!state.client) throw new Error('Client not initialized');
    
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'SET_ERROR', payload: null });

    try {
      const result = await state.client.authenticateDevice(deviceId);
      if (!result.success) {
        throw new Error(result.error || 'Authentication failed');
      }
      
      await state.client.connect();
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Authentication failed';
      dispatch({ type: 'SET_ERROR', payload: message });
      addToast('error', message, 5000);
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  /**
   * Join a table
   */
  const joinTable = async (tableId?: string) => {
    if (!state.client) throw new Error('Client not initialized');
    
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'SET_ERROR', payload: null });

    try {
      const result = await state.client.joinTable(tableId);
      if (!result.success) {
        throw new Error(result.error || 'Failed to join table');
      }
      
      addToast('success', 'Joined table successfully', 2000);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to join table';
      dispatch({ type: 'SET_ERROR', payload: message });
      addToast('error', message, 5000);
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  /**
   * Leave current table
   */
  const leaveTable = async () => {
    if (!state.client) return;
    
    try {
      await state.client.leaveTable();
      dispatch({ type: 'SET_TABLE_STATE', payload: null as any });
      addToast('info', 'Left table', 2000);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to leave table';
      addToast('error', message, 3000);
    }
  };

  /**
   * Place a bet
   */
  const placeBet = async (amount: number) => {
    if (!state.client) throw new Error('Client not initialized');
    
    try {
      await state.client.placeBet(amount);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to place bet';
      addToast('error', message, 3000);
    }
  };

  /**
   * Hit action
   */
  const hit = async () => {
    if (!state.client) throw new Error('Client not initialized');
    
    try {
      await state.client.hit();
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to hit';
      addToast('error', message, 3000);
    }
  };

  /**
   * Stand action
   */
  const stand = async () => {
    if (!state.client) throw new Error('Client not initialized');
    
    try {
      await state.client.stand();
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to stand';
      addToast('error', message, 3000);
    }
  };

  /**
   * Double action
   */
  const double = async () => {
    if (!state.client) throw new Error('Client not initialized');
    
    try {
      await state.client.double();
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to double';
      addToast('error', message, 3000);
    }
  };

  /**
   * Add a toast notification
   */
  const addToast = (type: ToastNotification['type'], message: string, duration: number = 4000) => {
    const toast: ToastNotification = {
      id: `toast_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      type,
      message,
      duration,
      timestamp: new Date()
    };
    
    dispatch({ type: 'ADD_TOAST', payload: toast });
    
    if (duration > 0) {
      setTimeout(() => {
        dispatch({ type: 'REMOVE_TOAST', payload: toast.id });
      }, duration);
    }
  };

  /**
   * Remove a toast notification
   */
  const removeToast = (id: string) => {
    dispatch({ type: 'REMOVE_TOAST', payload: id });
  };

  const contextValue = {
    state,
    dispatch,
    actions: {
      initializeClient,
      authenticateDevice,
      joinTable,
      leaveTable,
      placeBet,
      hit,
      stand,
      double,
      addToast,
      removeToast
    }
  };

  return (
    <GameContext.Provider value={contextValue}>
      {children}
    </GameContext.Provider>
  );
}

/**
 * Hook to use game context
 */
export function useGame() {
  const context = useContext(GameContext);
  if (!context) {
    throw new Error('useGame must be used within a GameProvider');
  }
  return context;
}
