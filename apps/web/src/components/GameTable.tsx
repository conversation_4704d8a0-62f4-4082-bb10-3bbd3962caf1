import React from 'react';
import { Hand } from './Hand.js';
import { Timer } from './Timer.js';
import { PlayerSeat } from './PlayerSeat.js';
import { useGame } from '../contexts/GameContext.js';
import { GamePhase, PHASE_TIMERS } from '@blackjack/game-core';
import { UIPlayerState } from '../types/index.js';
import clsx from 'clsx';

/**
 * Calculate player positions around the table
 */
function calculatePlayerPositions(seatIndex: number, totalSeats: number = 8): { x: number; y: number; rotation: number } {
  const angle = (seatIndex / totalSeats) * 2 * Math.PI - Math.PI / 2; // Start from top
  const radius = 200;
  const x = Math.cos(angle) * radius;
  const y = Math.sin(angle) * radius;
  const rotation = (angle * 180) / Math.PI + 90;
  
  return { x, y, rotation };
}

/**
 * Convert player state to UI player state
 */
function toUIPlayerState(player: any, localPlayerId: string | null): UIPlayerState {
  return {
    ...player,
    position: calculatePlayerPositions(player.seatIndex),
    isLocalPlayer: player.userId === localPlayerId
  };
}

/**
 * Main game table component
 */
export function GameTable() {
  const { state } = useGame();
  const { tableState, localPlayerId } = state;

  if (!tableState) {
    return (
      <div className="game-table game-table--empty">
        <div className="game-table__message">
          <h2>No table joined</h2>
          <p>Join a table to start playing</p>
        </div>
      </div>
    );
  }

  const uiPlayers = tableState.players.map(player => 
    toUIPlayerState(player, localPlayerId)
  );

  const localPlayer = uiPlayers.find(p => p.isLocalPlayer);
  const dealerHand = tableState.dealer.hand;
  
  const getPhaseDisplay = () => {
    switch (tableState.phase) {
      case GamePhase.BETTING:
        return 'Place Your Bets';
      case GamePhase.DEAL:
        return 'Dealing Cards';
      case GamePhase.PLAYER_TURNS:
        const activePlayer = uiPlayers.find(p => p.isActive);
        if (activePlayer?.isLocalPlayer) {
          return 'Your Turn';
        }
        return `${activePlayer?.displayName || 'Player'}'s Turn`;
      case GamePhase.DEALER_TURN:
        return 'Dealer Playing';
      case GamePhase.SETTLE:
        return 'Round Complete';
      case GamePhase.REVEAL:
        return 'Fairness Verification';
      default:
        return tableState.phase;
    }
  };

  const tableClasses = clsx(
    'game-table',
    `game-table--${tableState.phase.toLowerCase()}`,
    {
      'game-table--active': tableState.phase === GamePhase.PLAYER_TURNS,
      'game-table--betting': tableState.phase === GamePhase.BETTING
    }
  );

  return (
    <div className={tableClasses}>
      {/* Table surface */}
      <div className="game-table__surface">
        
        {/* Dealer area */}
        <div className="game-table__dealer">
          <div className="dealer">
            <div className="dealer__label">Dealer</div>
            <Hand
              cards={dealerHand.cards}
              value={dealerHand.value}
              isSoft={dealerHand.isSoft}
              isBusted={dealerHand.isBusted}
              isBlackjack={dealerHand.isBlackjack}
              showValue={tableState.dealer.hasRevealed || tableState.phase === GamePhase.SETTLE}
              size="medium"
              className="dealer__hand"
            />
          </div>
        </div>

        {/* Player seats */}
        <div className="game-table__seats">
          {Array.from({ length: 8 }, (_, index) => {
            const player = uiPlayers.find(p => p.seatIndex === index);
            return (
              <PlayerSeat
                key={index}
                player={player || null}
                seatIndex={index}
                isActive={player?.isActive || false}
                isLocalPlayer={player?.isLocalPlayer || false}
              />
            );
          })}
        </div>

        {/* Center info */}
        <div className="game-table__center">
          <div className="table-info">
            <div className="table-info__phase">
              {getPhaseDisplay()}
            </div>
            
            {tableState.timeLeftMs > 0 && (
              <Timer
                timeLeft={tableState.timeLeftMs}
                totalTime={PHASE_TIMERS[tableState.phase]}
                size={80}
                visible={true}
                color={tableState.timeLeftMs < 3000 ? 'danger' : 'primary'}
              />
            )}
            
            <div className="table-info__details">
              <div className="table-info__round">
                Round: {tableState.roundId.split('_')[1]?.substring(0, 8) || 'N/A'}
              </div>
              <div className="table-info__shoe">
                Cards: {tableState.shoeDepth}
              </div>
            </div>
          </div>
        </div>

        {/* Local player highlight */}
        {localPlayer && (
          <div 
            className="game-table__local-player-indicator"
            style={{
              transform: `translate(${localPlayer.position.x}px, ${localPlayer.position.y}px)`
            }}
          >
            <div className="local-player-indicator">
              You
            </div>
          </div>
        )}
      </div>

      {/* Table felt pattern */}
      <div className="game-table__felt"></div>
    </div>
  );
}

/**
 * CSS styles for the game table component
 */
export const gameTableStyles = `
.game-table {
  position: relative;
  width: 100%;
  max-width: 800px;
  height: 600px;
  margin: 0 auto;
  border-radius: 200px;
  background: linear-gradient(135deg, #065f46, #047857);
  border: 8px solid #92400e;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  overflow: hidden;
}

.game-table__surface {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.game-table__felt {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
    radial-gradient(circle at 70% 70%, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
  opacity: 0.3;
  pointer-events: none;
}

.game-table__dealer {
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;
}

.dealer {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.dealer__label {
  color: white;
  font-weight: bold;
  font-size: 1.1em;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.dealer__hand {
  background: rgba(0, 0, 0, 0.2);
  padding: 8px;
  border-radius: 8px;
}

.game-table__seats {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.game-table__center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}

.table-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  background: rgba(0, 0, 0, 0.3);
  padding: 16px;
  border-radius: 12px;
  backdrop-filter: blur(4px);
}

.table-info__phase {
  color: white;
  font-weight: bold;
  font-size: 1.2em;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.table-info__details {
  display: flex;
  gap: 16px;
  font-size: 0.9em;
  color: rgba(255, 255, 255, 0.8);
}

.game-table__local-player-indicator {
  position: absolute;
  z-index: 3;
  pointer-events: none;
}

.local-player-indicator {
  background: #fbbf24;
  color: #92400e;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8em;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transform: translateY(-40px);
}

.game-table--empty {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #374151, #4b5563);
}

.game-table__message {
  text-align: center;
  color: white;
}

.game-table__message h2 {
  margin: 0 0 8px 0;
  font-size: 1.5em;
}

.game-table__message p {
  margin: 0;
  opacity: 0.8;
}

/* Responsive design */
@media (max-width: 768px) {
  .game-table {
    height: 400px;
    max-width: 600px;
  }
  
  .dealer__label {
    font-size: 1em;
  }
  
  .table-info__phase {
    font-size: 1em;
  }
  
  .table-info__details {
    font-size: 0.8em;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .game-table {
    height: 300px;
    max-width: 400px;
  }
  
  .table-info {
    padding: 12px;
    gap: 8px;
  }
  
  .table-info__details {
    flex-direction: column;
    gap: 4px;
    text-align: center;
  }
}
`;
