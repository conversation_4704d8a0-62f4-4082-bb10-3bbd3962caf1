import React from 'react';
import { Card } from './Card.js';
import { HandProps } from '../types/index.js';
import clsx from 'clsx';

/**
 * Hand component - displays a collection of cards with value
 */
export function Hand({
  cards,
  value,
  isSoft,
  isBusted,
  isBlackjack,
  showValue = true,
  size = 'medium',
  orientation = 'horizontal',
  className
}: HandProps) {
  const handClasses = clsx(
    'hand',
    `hand--${orientation}`,
    `hand--${size}`,
    {
      'hand--busted': isBusted,
      'hand--blackjack': isBlackjack,
      'hand--soft': isSoft && !isBusted && !isBlackjack
    },
    className
  );

  const getValueDisplay = () => {
    if (isBusted) {
      return `${value} (BUST)`;
    }
    if (isBlackjack) {
      return `${value} (BLACKJACK)`;
    }
    if (isSoft && value !== 21) {
      return `${value} (SOFT)`;
    }
    return value.toString();
  };

  const getValueColor = () => {
    if (isBusted) return 'text-red-600';
    if (isBlackjack) return 'text-yellow-600';
    if (isSoft) return 'text-blue-600';
    return 'text-gray-800';
  };

  return (
    <div className={handClasses}>
      <div className="hand__cards">
        {cards.map((card, index) => (
          <Card
            key={`${card.suit}-${card.rank}-${index}`}
            card={card}
            size={size}
            className={clsx('hand__card', {
              'hand__card--stacked': orientation === 'horizontal' && index > 0
            })}
          />
        ))}
      </div>
      
      {showValue && cards.length > 0 && (
        <div className={clsx('hand__value', getValueColor())}>
          {getValueDisplay()}
        </div>
      )}
    </div>
  );
}

/**
 * CSS styles for the hand component
 */
export const handStyles = `
.hand {
  display: flex;
  align-items: center;
  gap: 8px;
}

.hand--horizontal {
  flex-direction: row;
}

.hand--vertical {
  flex-direction: column;
}

.hand__cards {
  display: flex;
  position: relative;
}

.hand--horizontal .hand__cards {
  flex-direction: row;
}

.hand--vertical .hand__cards {
  flex-direction: column;
  gap: 4px;
}

.hand__card--stacked {
  margin-left: -20px;
}

.hand--small .hand__card--stacked {
  margin-left: -15px;
}

.hand--large .hand__card--stacked {
  margin-left: -25px;
}

.hand__value {
  font-weight: bold;
  font-size: 1.1em;
  padding: 4px 8px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #e5e7eb;
  min-width: 60px;
  text-align: center;
}

.hand--small .hand__value {
  font-size: 0.9em;
  padding: 2px 6px;
  min-width: 50px;
}

.hand--large .hand__value {
  font-size: 1.3em;
  padding: 6px 12px;
  min-width: 80px;
}

.hand--busted .hand__value {
  background: rgba(239, 68, 68, 0.1);
  border-color: #ef4444;
  color: #dc2626;
}

.hand--blackjack .hand__value {
  background: rgba(245, 158, 11, 0.1);
  border-color: #f59e0b;
  color: #d97706;
}

.hand--soft .hand__value {
  background: rgba(59, 130, 246, 0.1);
  border-color: #3b82f6;
  color: #2563eb;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .hand {
    gap: 4px;
  }
  
  .hand__card--stacked {
    margin-left: -15px;
  }
  
  .hand--small .hand__card--stacked {
    margin-left: -10px;
  }
  
  .hand__value {
    font-size: 1em;
    padding: 3px 6px;
    min-width: 50px;
  }
}

/* Animation for new cards */
.hand__card {
  animation: cardDeal 0.3s ease-out;
}

@keyframes cardDeal {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}
`;
