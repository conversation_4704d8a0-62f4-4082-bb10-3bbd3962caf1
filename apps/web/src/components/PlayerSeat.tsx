import React from 'react';
import { Hand } from './Hand.js';
import { PlayerSeatProps } from '../types/index.js';
import clsx from 'clsx';

/**
 * Calculate seat position on the table
 */
function getSeatPosition(seatIndex: number): { x: number; y: number; rotation: number } {
  // Arrange seats in a semi-circle around the bottom of the table
  const totalSeats = 8;
  const startAngle = Math.PI * 0.2; // Start angle (slightly left of bottom)
  const endAngle = Math.PI * 0.8;   // End angle (slightly right of bottom)
  const angleRange = endAngle - startAngle;
  const angle = startAngle + (seatIndex / (totalSeats - 1)) * angleRange;
  
  const radius = 250;
  const centerX = 400; // Half of table width
  const centerY = 300; // Table height
  
  const x = centerX + Math.cos(angle) * radius;
  const y = centerY + Math.sin(angle) * radius;
  const rotation = (angle * 180) / Math.PI - 90;
  
  return { x, y, rotation };
}

/**
 * Player seat component
 */
export function PlayerSeat({
  player,
  seatIndex,
  isActive,
  isLocalPlayer,
  onSeatClick
}: PlayerSeatProps) {
  const position = getSeatPosition(seatIndex);
  const isEmpty = !player;

  const seatClasses = clsx(
    'player-seat',
    {
      'player-seat--empty': isEmpty,
      'player-seat--occupied': !isEmpty,
      'player-seat--active': isActive,
      'player-seat--local': isLocalPlayer,
      'player-seat--clickable': isEmpty && onSeatClick
    }
  );

  const handleClick = () => {
    if (isEmpty && onSeatClick) {
      onSeatClick();
    }
  };

  return (
    <div
      className={seatClasses}
      style={{
        position: 'absolute',
        left: position.x,
        top: position.y,
        transform: `translate(-50%, -50%) rotate(${position.rotation}deg)`
      }}
      onClick={handleClick}
    >
      {isEmpty ? (
        <div className="player-seat__empty">
          <div className="empty-seat">
            <div className="empty-seat__icon">+</div>
            <div className="empty-seat__text">Join</div>
          </div>
        </div>
      ) : (
        <div className="player-seat__occupied">
          <div className="player-info">
            <div className="player-info__name">
              {player.displayName}
            </div>
            
            {player.currentBet > 0 && (
              <div className="player-info__bet">
                Bet: {player.currentBet}
              </div>
            )}
            
            <div className="player-info__balance">
              {player.balance} chips
            </div>
            
            {!player.isConnected && (
              <div className="player-info__status">
                Disconnected
              </div>
            )}
          </div>
          
          {player.hand.cards.length > 0 && (
            <div className="player-hand">
              <Hand
                cards={player.hand.cards}
                value={player.hand.value}
                isSoft={player.hand.isSoft}
                isBusted={player.hand.isBusted}
                isBlackjack={player.hand.isBlackjack}
                showValue={true}
                size="small"
                orientation="horizontal"
              />
            </div>
          )}
          
          {isActive && (
            <div className="player-seat__active-indicator">
              <div className="active-indicator"></div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

/**
 * CSS styles for the player seat component
 */
export const playerSeatStyles = `
.player-seat {
  width: 120px;
  min-height: 80px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  z-index: 2;
}

.player-seat--clickable {
  cursor: pointer;
}

.player-seat--clickable:hover .empty-seat {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.player-seat__empty {
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-seat {
  width: 60px;
  height: 60px;
  border: 2px dashed rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  transition: all 0.2s ease;
  color: white;
}

.empty-seat__icon {
  font-size: 1.5em;
  font-weight: bold;
  line-height: 1;
}

.empty-seat__text {
  font-size: 0.7em;
  margin-top: 2px;
}

.player-seat__occupied {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  position: relative;
}

.player-info {
  background: rgba(0, 0, 0, 0.7);
  padding: 8px 12px;
  border-radius: 8px;
  text-align: center;
  min-width: 100px;
  backdrop-filter: blur(4px);
}

.player-info__name {
  color: white;
  font-weight: bold;
  font-size: 0.9em;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100px;
}

.player-info__bet {
  color: #fbbf24;
  font-size: 0.8em;
  font-weight: bold;
  margin-bottom: 2px;
}

.player-info__balance {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.7em;
}

.player-info__status {
  color: #ef4444;
  font-size: 0.7em;
  font-style: italic;
  margin-top: 2px;
}

.player-hand {
  transform: rotate(0deg); /* Counter-rotate to keep cards upright */
}

.player-seat--local .player-info {
  background: rgba(251, 191, 36, 0.2);
  border: 1px solid #fbbf24;
}

.player-seat--local .player-info__name {
  color: #fbbf24;
}

.player-seat--active .player-info {
  background: rgba(34, 197, 94, 0.2);
  border: 1px solid #22c55e;
  animation: activePlayerPulse 2s ease-in-out infinite;
}

.player-seat__active-indicator {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
}

.active-indicator {
  width: 12px;
  height: 12px;
  background: #22c55e;
  border-radius: 50%;
  animation: activePulse 1s ease-in-out infinite;
  box-shadow: 0 0 8px #22c55e;
}

/* Animations */
@keyframes activePlayerPulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.4);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(34, 197, 94, 0);
  }
}

@keyframes activePulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .player-seat {
    width: 100px;
    min-height: 60px;
  }
  
  .empty-seat {
    width: 50px;
    height: 50px;
  }
  
  .empty-seat__icon {
    font-size: 1.2em;
  }
  
  .empty-seat__text {
    font-size: 0.6em;
  }
  
  .player-info {
    padding: 6px 8px;
    min-width: 80px;
  }
  
  .player-info__name {
    font-size: 0.8em;
    max-width: 80px;
  }
  
  .player-info__bet {
    font-size: 0.7em;
  }
  
  .player-info__balance {
    font-size: 0.6em;
  }
}

@media (max-width: 480px) {
  .player-seat {
    width: 80px;
    min-height: 50px;
  }
  
  .empty-seat {
    width: 40px;
    height: 40px;
  }
  
  .empty-seat__icon {
    font-size: 1em;
  }
  
  .empty-seat__text {
    font-size: 0.5em;
  }
  
  .player-info {
    padding: 4px 6px;
    min-width: 60px;
  }
  
  .player-info__name {
    font-size: 0.7em;
    max-width: 60px;
  }
  
  .player-info__bet,
  .player-info__balance {
    font-size: 0.6em;
  }
}
`;
