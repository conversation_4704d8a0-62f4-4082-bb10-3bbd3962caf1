import React from 'react';
import { Card as CardType, Suit } from '@blackjack/game-core';
import { CardProps } from '../types/index.js';
import clsx from 'clsx';

/**
 * Get suit symbol
 */
function getSuitSymbol(suit: Suit): string {
  switch (suit) {
    case Suit.HEARTS:
      return '♥';
    case Suit.DIAMONDS:
      return '♦';
    case Suit.CLUBS:
      return '♣';
    case Suit.SPADES:
      return '♠';
    default:
      return '';
  }
}

/**
 * Get suit color
 */
function getSuitColor(suit: Suit): 'red' | 'black' {
  return suit === Suit.HEARTS || suit === Suit.DIAMONDS ? 'red' : 'black';
}

/**
 * Card component
 */
export function Card({ 
  card, 
  faceDown = false, 
  size = 'medium', 
  animation, 
  onClick, 
  className 
}: CardProps) {
  const isVisible = card.faceUp && !faceDown;
  const suitSymbol = getSuitSymbol(card.suit);
  const suitColor = getSuitColor(card.suit);

  const cardClasses = clsx(
    'card',
    `card--${size}`,
    {
      'card--face-down': !isVisible,
      'card--red': isVisible && suitColor === 'red',
      'card--black': isVisible && suitColor === 'black',
      'card--clickable': onClick,
      'card--animating': animation
    },
    className
  );

  const handleClick = () => {
    if (onClick && !animation) {
      onClick();
    }
  };

  return (
    <div 
      className={cardClasses}
      onClick={handleClick}
      style={{
        transform: animation ? `translate(${animation.to.x}px, ${animation.to.y}px)` : undefined,
        transition: animation ? `transform ${animation.duration}ms ease-in-out` : undefined
      }}
    >
      <div className="card__inner">
        <div className="card__front">
          {isVisible ? (
            <>
              <div className="card__rank card__rank--top-left">
                {card.rank}
              </div>
              <div className="card__suit card__suit--top-left">
                {suitSymbol}
              </div>
              <div className="card__center">
                <div className="card__suit card__suit--center">
                  {suitSymbol}
                </div>
              </div>
              <div className="card__rank card__rank--bottom-right">
                {card.rank}
              </div>
              <div className="card__suit card__suit--bottom-right">
                {suitSymbol}
              </div>
            </>
          ) : (
            <div className="card__back">
              <div className="card__back-pattern"></div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

/**
 * CSS styles for the card component
 * This would typically be in a separate CSS file
 */
export const cardStyles = `
.card {
  position: relative;
  display: inline-block;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  user-select: none;
}

.card--small {
  width: 40px;
  height: 56px;
  font-size: 10px;
}

.card--medium {
  width: 60px;
  height: 84px;
  font-size: 14px;
}

.card--large {
  width: 80px;
  height: 112px;
  font-size: 18px;
}

.card--clickable {
  cursor: pointer;
}

.card--clickable:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.card__inner {
  width: 100%;
  height: 100%;
  position: relative;
  border-radius: 8px;
  background: white;
  border: 1px solid #ddd;
}

.card__front {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 4px;
}

.card__rank {
  font-weight: bold;
  line-height: 1;
}

.card__suit {
  line-height: 1;
}

.card__rank--top-left,
.card__suit--top-left {
  position: absolute;
  top: 4px;
  left: 4px;
}

.card__suit--top-left {
  top: 16px;
}

.card__rank--bottom-right,
.card__suit--bottom-right {
  position: absolute;
  bottom: 4px;
  right: 4px;
  transform: rotate(180deg);
}

.card__suit--bottom-right {
  bottom: 16px;
}

.card__center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.card__suit--center {
  font-size: 1.5em;
}

.card--red {
  color: #dc2626;
}

.card--black {
  color: #1f2937;
}

.card--face-down .card__inner {
  background: linear-gradient(45deg, #1e40af, #3b82f6);
}

.card__back {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.card__back-pattern {
  width: 80%;
  height: 80%;
  background: repeating-linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0.1) 2px,
    transparent 2px,
    transparent 4px
  );
  border-radius: 4px;
}

.card--animating {
  z-index: 10;
}

@media (max-width: 768px) {
  .card--medium {
    width: 50px;
    height: 70px;
    font-size: 12px;
  }
  
  .card--large {
    width: 60px;
    height: 84px;
    font-size: 14px;
  }
}
`;
