import React, { useEffect, useState } from 'react';
import { TimerProps } from '../types/index.js';
import clsx from 'clsx';

/**
 * Circular countdown timer component
 */
export function Timer({
  timeLeft,
  totalTime,
  size = 60,
  visible = true,
  color = 'primary'
}: TimerProps) {
  const [displayTime, setDisplayTime] = useState(timeLeft);

  useEffect(() => {
    setDisplayTime(timeLeft);
  }, [timeLeft]);

  if (!visible || totalTime <= 0) {
    return null;
  }

  const progress = Math.max(0, Math.min(1, displayTime / totalTime));
  const circumference = 2 * Math.PI * (size / 2 - 4);
  const strokeDashoffset = circumference * (1 - progress);
  
  const seconds = Math.ceil(displayTime / 1000);
  const isWarning = progress < 0.3;
  const isDanger = progress < 0.1;

  const getColorClass = () => {
    if (isDanger) return 'timer--danger';
    if (isWarning) return 'timer--warning';
    return `timer--${color}`;
  };

  const timerClasses = clsx(
    'timer',
    getColorClass(),
    {
      'timer--warning-pulse': isWarning && !isDanger,
      'timer--danger-pulse': isDanger
    }
  );

  return (
    <div className={timerClasses} style={{ width: size, height: size }}>
      <svg
        className="timer__svg"
        width={size}
        height={size}
        viewBox={`0 0 ${size} ${size}`}
      >
        {/* Background circle */}
        <circle
          className="timer__background"
          cx={size / 2}
          cy={size / 2}
          r={size / 2 - 4}
          fill="none"
          strokeWidth="4"
        />
        
        {/* Progress circle */}
        <circle
          className="timer__progress"
          cx={size / 2}
          cy={size / 2}
          r={size / 2 - 4}
          fill="none"
          strokeWidth="4"
          strokeLinecap="round"
          strokeDasharray={circumference}
          strokeDashoffset={strokeDashoffset}
          transform={`rotate(-90 ${size / 2} ${size / 2})`}
        />
      </svg>
      
      <div className="timer__text">
        {seconds}
      </div>
    </div>
  );
}

/**
 * CSS styles for the timer component
 */
export const timerStyles = `
.timer {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.timer__svg {
  position: absolute;
  top: 0;
  left: 0;
}

.timer__background {
  stroke: rgba(0, 0, 0, 0.1);
}

.timer__progress {
  transition: stroke-dashoffset 0.1s ease-in-out;
}

.timer--primary .timer__progress {
  stroke: #3b82f6;
}

.timer--warning .timer__progress {
  stroke: #f59e0b;
}

.timer--danger .timer__progress {
  stroke: #ef4444;
}

.timer__text {
  font-weight: bold;
  font-size: 1.2em;
  color: #1f2937;
  z-index: 1;
}

.timer--warning .timer__text {
  color: #d97706;
}

.timer--danger .timer__text {
  color: #dc2626;
}

/* Pulsing animations */
.timer--warning-pulse {
  animation: warningPulse 1s ease-in-out infinite;
}

.timer--danger-pulse {
  animation: dangerPulse 0.5s ease-in-out infinite;
}

@keyframes warningPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.9;
  }
}

@keyframes dangerPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

/* Responsive sizing */
@media (max-width: 768px) {
  .timer__text {
    font-size: 1em;
  }
}
`;
