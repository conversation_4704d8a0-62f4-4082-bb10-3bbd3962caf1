import React from 'react';
import { ChipProps } from '../types/index.js';
import clsx from 'clsx';

/**
 * Get chip color based on value
 */
function getChipColor(value: number): string {
  if (value >= 5000) return 'purple';
  if (value >= 1000) return 'orange';
  if (value >= 500) return 'pink';
  if (value >= 200) return 'blue';
  if (value >= 100) return 'green';
  if (value >= 50) return 'red';
  return 'white';
}

/**
 * Format chip value for display
 */
function formatChipValue(value: number): string {
  if (value >= 1000) {
    return `${value / 1000}K`;
  }
  return value.toString();
}

/**
 * Betting chip component
 */
export function Chip({
  value,
  selected = false,
  disabled = false,
  onClick,
  size = 'medium'
}: ChipProps) {
  const color = getChipColor(value);
  const displayValue = formatChipValue(value);

  const chipClasses = clsx(
    'chip',
    `chip--${color}`,
    `chip--${size}`,
    {
      'chip--selected': selected,
      'chip--disabled': disabled,
      'chip--clickable': onClick && !disabled
    }
  );

  const handleClick = () => {
    if (onClick && !disabled) {
      onClick();
    }
  };

  return (
    <button
      className={chipClasses}
      onClick={handleClick}
      disabled={disabled}
      type="button"
      aria-label={`Bet ${value} chips`}
    >
      <div className="chip__inner">
        <div className="chip__value">
          {displayValue}
        </div>
        <div className="chip__dots">
          <div className="chip__dot"></div>
          <div className="chip__dot"></div>
          <div className="chip__dot"></div>
        </div>
      </div>
    </button>
  );
}

/**
 * CSS styles for the chip component
 */
export const chipStyles = `
.chip {
  position: relative;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  background: none;
  padding: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.chip--small {
  width: 40px;
  height: 40px;
}

.chip--medium {
  width: 50px;
  height: 50px;
}

.chip--large {
  width: 60px;
  height: 60px;
}

.chip__inner {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  border: 3px solid rgba(255, 255, 255, 0.3);
}

.chip__value {
  font-weight: bold;
  font-size: 0.8em;
  color: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  z-index: 2;
}

.chip--small .chip__value {
  font-size: 0.7em;
}

.chip--large .chip__value {
  font-size: 0.9em;
}

.chip__dots {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  height: 80%;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: space-around;
  opacity: 0.3;
}

.chip__dot {
  width: 3px;
  height: 3px;
  border-radius: 50%;
  background: white;
}

/* Chip colors */
.chip--white .chip__inner {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  color: #1f2937;
}

.chip--white .chip__value {
  color: #1f2937;
}

.chip--red .chip__inner {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.chip--green .chip__inner {
  background: linear-gradient(135deg, #10b981, #059669);
}

.chip--blue .chip__inner {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.chip--pink .chip__inner {
  background: linear-gradient(135deg, #ec4899, #db2777);
}

.chip--orange .chip__inner {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.chip--purple .chip__inner {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

/* States */
.chip--clickable:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.chip--clickable:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.chip--selected {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
}

.chip--selected .chip__inner {
  border-color: rgba(255, 255, 255, 0.8);
}

.chip--disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .chip--medium {
    width: 45px;
    height: 45px;
  }
  
  .chip--large {
    width: 50px;
    height: 50px;
  }
  
  .chip__value {
    font-size: 0.7em;
  }
}

/* Animation for chip selection */
.chip--selected {
  animation: chipSelect 0.2s ease-out;
}

@keyframes chipSelect {
  0% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-5px) scale(1.1);
  }
  100% {
    transform: translateY(-3px) scale(1);
  }
}
`;
