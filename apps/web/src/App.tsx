import React, { useState, useEffect } from 'react';
import { GameProvider } from './contexts/GameContext';
import { BlackjackClient } from '@blackjack/client-sdk';
import './App.css';

// Simple demo component to test our setup
function BlackjackDemo() {
  const [client, setClient] = useState<BlackjackClient | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [status, setStatus] = useState('Initializing...');

  useEffect(() => {
    // Initialize the client
    const initClient = async () => {
      try {
        const config = {
          host: 'localhost',
          port: 7350,
          useSSL: false,
          serverKey: 'devkey'
        };

        const blackjackClient = new BlackjackClient(config);
        setClient(blackjackClient);

        // Test authentication
        const deviceId = `device_${Date.now()}`;
        const authResult = await blackjackClient.authenticateDevice(deviceId);

        if (authResult.success) {
          setStatus(`Authenticated as ${authResult.username}`);

          // Test connection
          await blackjackClient.connect();
          setIsConnected(true);
          setStatus('Connected and ready!');
        } else {
          setStatus(`Auth failed: ${authResult.error}`);
        }
      } catch (error) {
        setStatus(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    };

    initClient();
  }, []);

  const handleJoinTable = async () => {
    if (!client) return;

    try {
      setStatus('Joining table...');
      const result = await client.joinTable();
      if (result.success) {
        setStatus(`Joined table: ${result.matchId}`);
      } else {
        setStatus(`Failed to join: ${result.error}`);
      }
    } catch (error) {
      setStatus(`Error joining: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>🃏 Blackjack Multiplayer MVP</h1>

      <div style={{
        background: '#f5f5f5',
        padding: '15px',
        borderRadius: '8px',
        marginBottom: '20px'
      }}>
        <h3>Status</h3>
        <p>
          <strong>Connection:</strong> {isConnected ? '✅ Connected' : '❌ Disconnected'}
        </p>
        <p>
          <strong>Status:</strong> {status}
        </p>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <button
          onClick={handleJoinTable}
          disabled={!isConnected}
          style={{
            padding: '10px 20px',
            fontSize: '16px',
            backgroundColor: isConnected ? '#4CAF50' : '#ccc',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: isConnected ? 'pointer' : 'not-allowed'
          }}
        >
          Join Table
        </button>
      </div>

      <div style={{
        background: '#e8f4f8',
        padding: '15px',
        borderRadius: '8px'
      }}>
        <h3>🎯 MVP Features Implemented</h3>
        <ul>
          <li>✅ Game Core Types & Interfaces</li>
          <li>✅ Card & Deck Logic with Provably Fair RNG</li>
          <li>✅ Blackjack Game Engine</li>
          <li>✅ Nakama Server Match Handler</li>
          <li>✅ Wallet & Economy System</li>
          <li>✅ Client SDK (Mock Implementation)</li>
          <li>✅ React Component Architecture</li>
          <li>🚧 Game Table UI Components</li>
          <li>🚧 Timer & Phase Management</li>
          <li>🚧 Card Dealing Animations</li>
          <li>🚧 Audio System</li>
          <li>🚧 Toast Notifications</li>
          <li>🚧 Development Environment</li>
        </ul>
      </div>

      <div style={{
        marginTop: '20px',
        padding: '15px',
        background: '#fff3cd',
        borderRadius: '8px',
        border: '1px solid #ffeaa7'
      }}>
        <h4>🔧 Next Steps</h4>
        <p>
          This is a working foundation! The client SDK is currently using mock implementations
          to demonstrate the architecture. To complete the MVP:
        </p>
        <ol>
          <li>Set up Docker Compose with Nakama + CockroachDB</li>
          <li>Connect real Nakama client (replace mock implementations)</li>
          <li>Add betting panel and action controls</li>
          <li>Implement card animations and audio</li>
          <li>Add toast notifications and error handling</li>
        </ol>
      </div>
    </div>
  );
}

function App() {
  return (
    <GameProvider>
      <BlackjackDemo />
    </GameProvider>
  );
}

export default App;
