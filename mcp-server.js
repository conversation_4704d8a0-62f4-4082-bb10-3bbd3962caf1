#!/usr/bin/env node

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
} from '@modelcontextprotocol/sdk/types.js';

const server = new Server(
  {
    name: 'blackjack-game-server',
    version: '0.1.0',
  },
  {
    capabilities: {
      tools: {},
    },
  }
);

// List available tools
server.setRequestHandler(ListToolsRequestSchema, async () => {
  return {
    tools: [
      {
        name: 'get_game_info',
        description: 'Get information about the blackjack game',
        inputSchema: {
          type: 'object',
          properties: {},
        },
      },
      {
        name: 'help_with_code',
        description: 'Get help with blackjack game development',
        inputSchema: {
          type: 'object',
          properties: {
            question: {
              type: 'string',
              description: 'Your question about the game development',
            },
          },
          required: ['question'],
        },
      },
    ],
  };
});

// Handle tool calls
server.setRequestHandler(CallToolRequestSchema, async (request) => {
  const { name, arguments: args } = request.params;

  switch (name) {
    case 'get_game_info':
      return {
        content: [
          {
            type: 'text',
            text: 'Blackjack Multiplayer Game - React + TypeScript frontend with Nakama backend',
          },
        ],
      };

    case 'help_with_code':
      return {
        content: [
          {
            type: 'text',
            text: `I can help you with: ${args.question}. This is a multiplayer blackjack game built with React, TypeScript, and Nakama backend.`,
          },
        ],
      };

    default:
      throw new Error(`Unknown tool: ${name}`);
  }
});

async function runServer() {
  const transport = new StdioServerTransport();
  await server.connect(transport);
  console.error('Blackjack MCP server running on stdio');
}

runServer().catch(console.error);
