import { ClientEvent, ClientEventType, EventListener, EventListenerMap } from './types.js';

/**
 * Simple event emitter for client events
 */
export class EventEmitter {
  private listeners: Partial<EventListenerMap> = {};

  /**
   * Add an event listener
   */
  on<T extends ClientEventType>(
    eventType: T,
    listener: EventListener<Extract<ClientEvent, { type: T }>>
  ): void {
    if (!this.listeners[eventType]) {
      this.listeners[eventType] = [];
    }
    this.listeners[eventType]!.push(listener as any);
  }

  /**
   * Remove an event listener
   */
  off<T extends ClientEventType>(
    eventType: T,
    listener: EventListener<Extract<ClientEvent, { type: T }>>
  ): void {
    const listeners = this.listeners[eventType];
    if (listeners) {
      const index = listeners.indexOf(listener as any);
      if (index !== -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * Add a one-time event listener
   */
  once<T extends ClientEventType>(
    eventType: T,
    listener: EventListener<Extract<ClientEvent, { type: T }>>
  ): void {
    const onceListener = (event: Extract<ClientEvent, { type: T }>) => {
      listener(event);
      this.off(eventType, onceListener);
    };
    this.on(eventType, onceListener);
  }

  /**
   * Emit an event to all listeners
   */
  emit<T extends ClientEvent>(event: T): void {
    const listeners = this.listeners[event.type];
    if (listeners) {
      for (const listener of listeners) {
        try {
          listener(event as any);
        } catch (error) {
          console.error(`Error in event listener for ${event.type}:`, error);
        }
      }
    }
  }

  /**
   * Remove all listeners for a specific event type
   */
  removeAllListeners(eventType?: ClientEventType): void {
    if (eventType) {
      delete this.listeners[eventType];
    } else {
      this.listeners = {};
    }
  }

  /**
   * Get the number of listeners for an event type
   */
  listenerCount(eventType: ClientEventType): number {
    return this.listeners[eventType]?.length || 0;
  }

  /**
   * Get all event types that have listeners
   */
  eventNames(): ClientEventType[] {
    return Object.keys(this.listeners) as ClientEventType[];
  }
}
