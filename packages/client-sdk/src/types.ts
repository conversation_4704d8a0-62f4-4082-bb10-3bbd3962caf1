import { TableState, RoundResults, PlayerAction } from '@blackjack/game-core';

/**
 * Client configuration options
 */
export interface ClientConfig {
  /**
   * Nakama server host
   */
  host: string;
  /**
   * Nakama server port
   */
  port: number;
  /**
   * Whether to use SSL
   */
  useSSL: boolean;
  /**
   * Server key for authentication
   */
  serverKey: string;
  /**
   * Whether to enable verbose logging
   */
  verbose?: boolean;
}

/**
 * Connection state
 */
export enum ConnectionState {
  DISCONNECTED = 'DISCONNECTED',
  CONNECTING = 'CONNECTING',
  CONNECTED = 'CONNECTED',
  RECONNECTING = 'RECONNECTING',
  ERROR = 'ERROR'
}

/**
 * Authentication result
 */
export interface AuthResult {
  success: boolean;
  userId?: string;
  username?: string;
  error?: string;
}

/**
 * Table join result
 */
export interface JoinTableResult {
  success: boolean;
  matchId?: string;
  error?: string;
}

/**
 * Leaderboard entry from server
 */
export interface LeaderboardEntry {
  rank: number;
  userId: string;
  displayName: string;
  netWinnings: number;
  handsPlayed: number;
}

/**
 * Leaderboard response
 */
export interface LeaderboardResponse {
  success: boolean;
  leaderboard?: LeaderboardEntry[];
  period?: string;
  error?: string;
}

/**
 * Player statistics
 */
export interface PlayerStats {
  handsPlayed: number;
  handsWon: number;
  totalWagered: number;
  netWinnings: number;
  blackjacksHit: number;
  biggestWin: number;
  currentWinStreak: number;
  bestWinStreak: number;
  averageBet: number;
}

/**
 * Player stats response
 */
export interface PlayerStatsResponse {
  success: boolean;
  stats?: PlayerStats;
  error?: string;
}

/**
 * Client event types
 */
export enum ClientEventType {
  // Connection events
  CONNECTION_STATE_CHANGED = 'CONNECTION_STATE_CHANGED',
  AUTHENTICATED = 'AUTHENTICATED',
  AUTHENTICATION_FAILED = 'AUTHENTICATION_FAILED',
  
  // Table events
  TABLE_JOINED = 'TABLE_JOINED',
  TABLE_LEFT = 'TABLE_LEFT',
  TABLE_STATE_UPDATED = 'TABLE_STATE_UPDATED',
  
  // Game events
  ROUND_STARTED = 'ROUND_STARTED',
  CARD_DEALT = 'CARD_DEALT',
  ROUND_RESULT = 'ROUND_RESULT',
  
  // Wallet events
  WALLET_UPDATED = 'WALLET_UPDATED',
  
  // Error events
  ERROR = 'ERROR'
}

/**
 * Base event interface
 */
export interface BaseEvent {
  type: ClientEventType;
  timestamp: Date;
}

/**
 * Connection state changed event
 */
export interface ConnectionStateChangedEvent extends BaseEvent {
  type: ClientEventType.CONNECTION_STATE_CHANGED;
  state: ConnectionState;
  previousState: ConnectionState;
}

/**
 * Authenticated event
 */
export interface AuthenticatedEvent extends BaseEvent {
  type: ClientEventType.AUTHENTICATED;
  userId: string;
  username: string;
}

/**
 * Authentication failed event
 */
export interface AuthenticationFailedEvent extends BaseEvent {
  type: ClientEventType.AUTHENTICATION_FAILED;
  error: string;
}

/**
 * Table joined event
 */
export interface TableJoinedEvent extends BaseEvent {
  type: ClientEventType.TABLE_JOINED;
  matchId: string;
}

/**
 * Table left event
 */
export interface TableLeftEvent extends BaseEvent {
  type: ClientEventType.TABLE_LEFT;
  matchId: string;
}

/**
 * Table state updated event
 */
export interface TableStateUpdatedEvent extends BaseEvent {
  type: ClientEventType.TABLE_STATE_UPDATED;
  tableState: TableState;
}

/**
 * Round started event
 */
export interface RoundStartedEvent extends BaseEvent {
  type: ClientEventType.ROUND_STARTED;
  roundId: string;
  serverSeedHash: string;
  shoeDepth: number;
}

/**
 * Card dealt event
 */
export interface CardDealtEvent extends BaseEvent {
  type: ClientEventType.CARD_DEALT;
  to: 'player' | 'dealer';
  playerId?: string;
  card: any; // Card type from game-core
  handValue?: number;
}

/**
 * Round result event
 */
export interface RoundResultEvent extends BaseEvent {
  type: ClientEventType.ROUND_RESULT;
  results: RoundResults;
}

/**
 * Wallet updated event
 */
export interface WalletUpdatedEvent extends BaseEvent {
  type: ClientEventType.WALLET_UPDATED;
  chips: number;
  metadata: any;
}

/**
 * Error event
 */
export interface ErrorEvent extends BaseEvent {
  type: ClientEventType.ERROR;
  code: string;
  message: string;
}

/**
 * Union type for all client events
 */
export type ClientEvent = 
  | ConnectionStateChangedEvent
  | AuthenticatedEvent
  | AuthenticationFailedEvent
  | TableJoinedEvent
  | TableLeftEvent
  | TableStateUpdatedEvent
  | RoundStartedEvent
  | CardDealtEvent
  | RoundResultEvent
  | WalletUpdatedEvent
  | ErrorEvent;

/**
 * Event listener function type
 */
export type EventListener<T extends ClientEvent = ClientEvent> = (event: T) => void;

/**
 * Event listener map
 */
export type EventListenerMap = {
  [K in ClientEventType]: EventListener<Extract<ClientEvent, { type: K }>>[];
};
