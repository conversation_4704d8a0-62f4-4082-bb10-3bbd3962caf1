/**
 * Wallet transaction types as defined in the specification
 */
export enum WalletTransactionType {
  INITIAL_GRANT = 'initial_grant',
  BET_PAYOUT = 'bet_payout',
  FAUCET = 'faucet',
  ADMIN = 'admin'
}

/**
 * Base wallet transaction metadata
 */
export interface BaseWalletMetadata {
  type: WalletTransactionType;
  timestamp: Date;
  userId: string;
}

/**
 * Initial grant metadata (5000 chips for new players)
 */
export interface InitialGrantMetadata extends BaseWalletMetadata {
  type: WalletTransactionType.INITIAL_GRANT;
}

/**
 * Bet payout metadata (winnings/losses from settlement)
 */
export interface BetPayoutMetadata extends BaseWalletMetadata {
  type: WalletTransactionType.BET_PAYOUT;
  tableId: string;
  roundId: string;
  betAmount: number;
  payout: number;
  handResult: 'win' | 'lose' | 'push' | 'blackjack';
}

/**
 * Faucet metadata (auto top-up when balance = 0)
 */
export interface FaucetMetadata extends BaseWalletMetadata {
  type: WalletTransactionType.FAUCET;
  reason: 'zero_balance_auto_topup';
  tableId?: string;
  roundId?: string;
  amount: number; // Should always be 100 per spec
}

/**
 * Admin metadata (manual admin actions)
 */
export interface AdminMetadata extends BaseWalletMetadata {
  type: WalletTransactionType.ADMIN;
  reason: string;
  adminUserId: string;
  amount: number;
}

/**
 * Union type for all wallet metadata
 */
export type WalletMetadata = 
  | InitialGrantMetadata
  | BetPayoutMetadata
  | FaucetMetadata
  | AdminMetadata;

/**
 * Wallet transaction record
 */
export interface WalletTransaction {
  /**
   * Unique transaction ID
   */
  transactionId: string;
  /**
   * User ID this transaction belongs to
   */
  userId: string;
  /**
   * Amount of chips (positive for credits, negative for debits)
   */
  amount: number;
  /**
   * Balance after this transaction
   */
  balanceAfter: number;
  /**
   * Transaction metadata
   */
  metadata: WalletMetadata;
  /**
   * When the transaction was created
   */
  createdAt: Date;
}

/**
 * Wallet balance information
 */
export interface WalletBalance {
  /**
   * Current chip balance
   */
  chips: number;
  /**
   * Last transaction ID
   */
  lastTransactionId?: string;
  /**
   * When the balance was last updated
   */
  lastUpdated: Date;
}

/**
 * Wallet operation result
 */
export interface WalletOperationResult {
  /**
   * Whether the operation was successful
   */
  success: boolean;
  /**
   * New balance after operation
   */
  newBalance: number;
  /**
   * Transaction ID if successful
   */
  transactionId?: string;
  /**
   * Error message if failed
   */
  error?: string;
}

/**
 * Constants for wallet operations
 */
export const WALLET_CONSTANTS = {
  /**
   * Initial balance for new players
   */
  INITIAL_GRANT_AMOUNT: 5000,
  /**
   * Faucet amount when balance hits zero
   */
  FAUCET_AMOUNT: 100,
  /**
   * Minimum bet amount
   */
  MIN_BET: 25,
  /**
   * Nakama wallet key for chips
   */
  CHIPS_WALLET_KEY: 'chips'
} as const;

/**
 * Helper functions for wallet operations
 */

/**
 * Create initial grant metadata
 */
export function createInitialGrantMetadata(userId: string): InitialGrantMetadata {
  return {
    type: WalletTransactionType.INITIAL_GRANT,
    timestamp: new Date(),
    userId
  };
}

/**
 * Create bet payout metadata
 */
export function createBetPayoutMetadata(
  userId: string,
  tableId: string,
  roundId: string,
  betAmount: number,
  payout: number,
  handResult: 'win' | 'lose' | 'push' | 'blackjack'
): BetPayoutMetadata {
  return {
    type: WalletTransactionType.BET_PAYOUT,
    timestamp: new Date(),
    userId,
    tableId,
    roundId,
    betAmount,
    payout,
    handResult
  };
}

/**
 * Create faucet metadata
 */
export function createFaucetMetadata(
  userId: string,
  tableId?: string,
  roundId?: string
): FaucetMetadata {
  return {
    type: WalletTransactionType.FAUCET,
    timestamp: new Date(),
    userId,
    reason: 'zero_balance_auto_topup',
    tableId,
    roundId,
    amount: WALLET_CONSTANTS.FAUCET_AMOUNT
  };
}

/**
 * Create admin metadata
 */
export function createAdminMetadata(
  userId: string,
  adminUserId: string,
  amount: number,
  reason: string
): AdminMetadata {
  return {
    type: WalletTransactionType.ADMIN,
    timestamp: new Date(),
    userId,
    adminUserId,
    amount,
    reason
  };
}
