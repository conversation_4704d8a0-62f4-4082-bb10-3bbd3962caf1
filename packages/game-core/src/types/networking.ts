import { Card } from './card.js';
import { TableState, RoundResults, PlayerAction } from './game.js';

/**
 * Client to Server message opcodes
 */
export enum ClientOpcode {
  JOIN_TABLE = 1,
  PLACE_BET = 2,
  ACTION_HIT = 3,
  ACTION_STAND = 4,
  ACTION_DOUBLE = 5,
  LEAVE_TABLE = 6
}

/**
 * Server to Client message opcodes
 */
export enum ServerOpcode {
  TABLE_STATE = 101,
  ROUND_STARTED = 102,
  CARD_DEALT = 103,
  ROUND_RESULT = 104,
  WALLET_UPDATE = 105,
  ERROR_MSG = 106
}

/**
 * Client to Server Messages
 */

export interface JoinTableMessage {
  tableId?: string; // Optional for auto-match
}

export interface PlaceBetMessage {
  amount: number; // -1 for all-in
}

export interface ActionHitMessage {
  // Empty payload
}

export interface ActionStandMessage {
  // Empty payload
}

export interface ActionDoubleMessage {
  // Empty payload
}

export interface LeaveTableMessage {
  // Empty payload
}

/**
 * Union type for all client messages
 */
export type ClientMessage = 
  | { opcode: ClientOpcode.JOIN_TABLE; payload: JoinTableMessage }
  | { opcode: ClientOpcode.PLACE_BET; payload: PlaceBetMessage }
  | { opcode: ClientOpcode.ACTION_HIT; payload: ActionHitMessage }
  | { opcode: ClientOpcode.ACTION_STAND; payload: ActionStandMessage }
  | { opcode: ClientOpcode.ACTION_DOUBLE; payload: ActionDoubleMessage }
  | { opcode: ClientOpcode.LEAVE_TABLE; payload: LeaveTableMessage };

/**
 * Server to Client Messages
 */

export interface TableStateMessage {
  tableState: TableState;
}

export interface RoundStartedMessage {
  roundId: string;
  serverSeedHash: string;
  shoeDepth: number;
}

export interface CardDealtMessage {
  to: 'player' | 'dealer';
  playerId?: string; // Only present when to === 'player'
  card: Card;
  handValue?: number; // Current hand value after this card
}

export interface RoundResultMessage {
  results: RoundResults;
}

export interface WalletUpdateMessage {
  chips: number;
  metadata: WalletTransactionMetadata;
}

export interface ErrorMessage {
  code: ErrorCode;
  message: string;
}

/**
 * Union type for all server messages
 */
export type ServerMessage = 
  | { opcode: ServerOpcode.TABLE_STATE; payload: TableStateMessage }
  | { opcode: ServerOpcode.ROUND_STARTED; payload: RoundStartedMessage }
  | { opcode: ServerOpcode.CARD_DEALT; payload: CardDealtMessage }
  | { opcode: ServerOpcode.ROUND_RESULT; payload: RoundResultMessage }
  | { opcode: ServerOpcode.WALLET_UPDATE; payload: WalletUpdateMessage }
  | { opcode: ServerOpcode.ERROR_MSG; payload: ErrorMessage };

/**
 * Error codes for client-server communication
 */
export enum ErrorCode {
  // Table errors
  TABLE_FULL = 'TABLE_FULL',
  TABLE_NOT_FOUND = 'TABLE_NOT_FOUND',
  ALREADY_IN_TABLE = 'ALREADY_IN_TABLE',
  
  // Betting errors
  INSUFFICIENT_BALANCE = 'INSUFFICIENT_BALANCE',
  INVALID_BET_AMOUNT = 'INVALID_BET_AMOUNT',
  BET_TOO_LOW = 'BET_TOO_LOW',
  BET_TOO_HIGH = 'BET_TOO_HIGH',
  BETTING_CLOSED = 'BETTING_CLOSED',
  
  // Action errors
  INVALID_ACTION = 'INVALID_ACTION',
  NOT_YOUR_TURN = 'NOT_YOUR_TURN',
  ACTION_NOT_ALLOWED = 'ACTION_NOT_ALLOWED',
  GAME_NOT_IN_PROGRESS = 'GAME_NOT_IN_PROGRESS',
  
  // Connection errors
  CONNECTION_ERROR = 'CONNECTION_ERROR',
  AUTHENTICATION_FAILED = 'AUTHENTICATION_FAILED',
  
  // General errors
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  INVALID_REQUEST = 'INVALID_REQUEST'
}

/**
 * Wallet transaction metadata types
 */
export interface WalletTransactionMetadata {
  type: 'initial_grant' | 'bet_payout' | 'faucet' | 'admin';
  reason?: string;
  tableId?: string;
  roundId?: string;
  betAmount?: number;
  payout?: number;
}

/**
 * Helper functions for creating messages
 */

export function createClientMessage<T extends ClientOpcode>(
  opcode: T,
  payload: Extract<ClientMessage, { opcode: T }>['payload']
): Extract<ClientMessage, { opcode: T }> {
  return { opcode, payload } as Extract<ClientMessage, { opcode: T }>;
}

export function createServerMessage<T extends ServerOpcode>(
  opcode: T,
  payload: Extract<ServerMessage, { opcode: T }>['payload']
): Extract<ServerMessage, { opcode: T }> {
  return { opcode, payload } as Extract<ServerMessage, { opcode: T }>;
}
