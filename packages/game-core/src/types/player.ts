/**
 * Player profile information
 */
export interface PlayerProfile {
  /**
   * Unique user ID
   */
  userId: string;
  /**
   * Player's chosen display name
   */
  displayName: string;
  /**
   * Player's avatar URL (optional)
   */
  avatarUrl?: string;
  /**
   * Platform-specific ID (for future Discord/Steam integration)
   */
  platformId?: string;
  /**
   * When the player account was created
   */
  createdAt: Date;
  /**
   * Last time the player was active
   */
  lastActiveAt: Date;
}

/**
 * Player statistics
 */
export interface PlayerStats {
  /**
   * Total number of hands played
   */
  handsPlayed: number;
  /**
   * Total number of hands won
   */
  handsWon: number;
  /**
   * Total amount wagered (lifetime)
   */
  totalWagered: number;
  /**
   * Net winnings (excluding faucet grants)
   */
  netWinnings: number;
  /**
   * Number of blackjacks achieved
   */
  blackjacksHit: number;
  /**
   * Largest single win
   */
  biggestWin: number;
  /**
   * Current win streak
   */
  currentWinStreak: number;
  /**
   * Best win streak achieved
   */
  bestWinStreak: number;
  /**
   * Average bet size
   */
  averageBet: number;
}

/**
 * Leaderboard entry
 */
export interface LeaderboardEntry {
  /**
   * Player's user ID
   */
  userId: string;
  /**
   * Player's display name
   */
  displayName: string;
  /**
   * Player's avatar URL
   */
  avatarUrl?: string;
  /**
   * Net winnings for the leaderboard period
   */
  netWinnings: number;
  /**
   * Player's rank on the leaderboard
   */
  rank: number;
  /**
   * Number of hands played in this period
   */
  handsPlayed: number;
}

/**
 * Player preferences for UI/UX
 */
export interface PlayerPreferences {
  /**
   * Whether background music is muted
   */
  musicMuted: boolean;
  /**
   * Music volume (0.0 to 1.0)
   */
  musicVolume: number;
  /**
   * Whether to show card animations
   */
  showAnimations: boolean;
  /**
   * Whether to auto-stand on 21
   */
  autoStandOn21: boolean;
  /**
   * Preferred betting denomination for quick bets
   */
  preferredBetAmount: number;
}

/**
 * Quick bet chip denominations as defined in spec
 */
export const QUICK_BET_AMOUNTS = [25, 50, 100, 200, 500, 1000, 5000] as const;

/**
 * Type for quick bet amounts
 */
export type QuickBetAmount = typeof QUICK_BET_AMOUNTS[number];

/**
 * Minimum bet amount
 */
export const MIN_BET = 25;

/**
 * Starting balance for new players
 */
export const INITIAL_BALANCE = 5000;

/**
 * Faucet amount when balance reaches zero
 */
export const FAUCET_AMOUNT = 100;
