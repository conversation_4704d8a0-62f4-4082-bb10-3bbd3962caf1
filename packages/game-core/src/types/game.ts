import { Card } from './card.js';

/**
 * Game phases as defined in the specification
 */
export enum GamePhase {
  BETTING = 'BETTING',
  DEAL = 'DEAL',
  PLAYER_TURNS = 'PLAYER_TURNS',
  DEALER_TURN = 'DEALER_TURN',
  SETTLE = 'SETTLE',
  REVEAL = 'REVEAL'
}

/**
 * Player actions during their turn
 */
export enum PlayerAction {
  HIT = 'HIT',
  STAND = 'STAND',
  DOUBLE = 'DOUBLE',
  // Split will be added in later versions
  // SPLIT = 'SPLIT'
}

/**
 * Hand result types
 */
export enum HandResult {
  WIN = 'WIN',
  LOSE = 'LOSE',
  PUSH = 'PUSH',
  BLACKJACK = 'BLACKJACK',
  BUST = 'BUST'
}

/**
 * Represents a player's hand
 */
export interface Hand {
  cards: Card[];
  /**
   * Current hand value (best possible without busting)
   */
  value: number;
  /**
   * Whether this hand is soft (contains an Ace counted as 11)
   */
  isSoft: boolean;
  /**
   * Whether this hand is busted (over 21)
   */
  isBusted: boolean;
  /**
   * Whether this hand is blackjack (21 with 2 cards)
   */
  isBlackjack: boolean;
  /**
   * Whether this hand is finished (player stood or busted)
   */
  isFinished: boolean;
}

/**
 * Player state in the game
 */
export interface PlayerState {
  /**
   * Player's user ID
   */
  userId: string;
  /**
   * Player's display name
   */
  displayName: string;
  /**
   * Player's avatar URL (optional)
   */
  avatarUrl?: string;
  /**
   * Player's current hand
   */
  hand: Hand;
  /**
   * Current bet amount for this round
   */
  currentBet: number;
  /**
   * Player's chip balance
   */
  balance: number;
  /**
   * Whether it's currently this player's turn
   */
  isActive: boolean;
  /**
   * Whether the player has finished their turn
   */
  hasFinished: boolean;
  /**
   * Whether the player is connected
   */
  isConnected: boolean;
  /**
   * Player's seat position (0-7)
   */
  seatIndex: number;
}

/**
 * Dealer state
 */
export interface DealerState {
  /**
   * Dealer's hand
   */
  hand: Hand;
  /**
   * Whether the dealer has revealed their hole card
   */
  hasRevealed: boolean;
}

/**
 * Complete table state
 */
export interface TableState {
  /**
   * Unique table/match ID
   */
  tableId: string;
  /**
   * Current game phase
   */
  phase: GamePhase;
  /**
   * Current round ID
   */
  roundId: string;
  /**
   * Players at the table (max 8)
   */
  players: PlayerState[];
  /**
   * Dealer state
   */
  dealer: DealerState;
  /**
   * ID of the currently acting player (during PLAYER_TURNS)
   */
  actingPlayerId?: string;
  /**
   * Time remaining in current phase/turn (milliseconds)
   */
  timeLeftMs: number;
  /**
   * Number of cards remaining in the shoe
   */
  shoeDepth: number;
  /**
   * Server seed hash for provably fair verification
   */
  serverSeedHash?: string;
  /**
   * Minimum bet for this table
   */
  minBet: number;
  /**
   * Maximum bet for this table
   */
  maxBet: number;
  /**
   * Table name/identifier
   */
  tableName: string;
  /**
   * Number of spectators
   */
  spectatorCount: number;
}

/**
 * Round result for a single player
 */
export interface PlayerResult {
  userId: string;
  handResult: HandResult;
  betAmount: number;
  payout: number;
  finalHandValue: number;
}

/**
 * Complete round results
 */
export interface RoundResults {
  roundId: string;
  dealerFinalValue: number;
  dealerBusted: boolean;
  playerResults: PlayerResult[];
  serverSeed: string;
  roundNonce: number;
}

/**
 * Timer configuration for different phases
 */
export const PHASE_TIMERS = {
  [GamePhase.BETTING]: 10000, // 10 seconds
  [GamePhase.DEAL]: 3000, // 3 seconds for dealing animation
  [GamePhase.PLAYER_TURNS]: 12000, // 12 seconds per player
  [GamePhase.DEALER_TURN]: 2000, // 2 seconds per dealer action
  [GamePhase.SETTLE]: 3000, // 3 seconds to show results
  [GamePhase.REVEAL]: 2000, // 2 seconds to show seed reveal
} as const;
