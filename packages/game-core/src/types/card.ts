/**
 * Card suits in a standard deck
 */
export enum Suit {
  HEARTS = 'hearts',
  DIAMONDS = 'diamonds',
  CLUBS = 'clubs',
  SPADES = 'spades'
}

/**
 * Card ranks with their string representations
 */
export enum Rank {
  ACE = 'A',
  TWO = '2',
  THREE = '3',
  FOUR = '4',
  FIVE = '5',
  SIX = '6',
  SEVEN = '7',
  EIGHT = '8',
  NINE = '9',
  TEN = '10',
  JACK = 'J',
  QUEEN = 'Q',
  KING = 'K'
}

/**
 * Represents a playing card
 */
export interface Card {
  suit: Suit;
  rank: Rank;
  /**
   * Whether the card is face up (visible to players)
   */
  faceUp: boolean;
}

/**
 * Utility type for card values in blackjack
 */
export interface CardValue {
  /**
   * The low value (Ace = 1, Face cards = 10)
   */
  low: number;
  /**
   * The high value (Ace = 11, Face cards = 10)
   */
  high: number;
}

/**
 * Get the blackjack values for a card rank
 */
export function getCardValue(rank: Rank): CardValue {
  switch (rank) {
    case Rank.ACE:
      return { low: 1, high: 11 };
    case Rank.TWO:
      return { low: 2, high: 2 };
    case Rank.THREE:
      return { low: 3, high: 3 };
    case Rank.FOUR:
      return { low: 4, high: 4 };
    case Rank.FIVE:
      return { low: 5, high: 5 };
    case Rank.SIX:
      return { low: 6, high: 6 };
    case Rank.SEVEN:
      return { low: 7, high: 7 };
    case Rank.EIGHT:
      return { low: 8, high: 8 };
    case Rank.NINE:
      return { low: 9, high: 9 };
    case Rank.TEN:
    case Rank.JACK:
    case Rank.QUEEN:
    case Rank.KING:
      return { low: 10, high: 10 };
    default:
      throw new Error(`Invalid card rank: ${rank}`);
  }
}

/**
 * Create a new card
 */
export function createCard(suit: Suit, rank: Rank, faceUp: boolean = true): Card {
  return { suit, rank, faceUp };
}

/**
 * Get all possible cards in a standard deck
 */
export function getAllCards(): Card[] {
  const cards: Card[] = [];
  for (const suit of Object.values(Suit)) {
    for (const rank of Object.values(Rank)) {
      cards.push(createCard(suit, rank));
    }
  }
  return cards;
}
