import { Card, getCardValue } from '../types/card.js';
import { Hand, HandResult, PlayerAction } from '../types/game.js';

/**
 * Calculate the best possible value for a hand without busting
 */
export function calculateHandValue(cards: Card[]): { value: number; isSoft: boolean } {
  let total = 0;
  let aces = 0;
  
  // Count non-ace cards first
  for (const card of cards) {
    const cardValue = getCardValue(card.rank);
    if (card.rank === 'A') {
      aces++;
    } else {
      total += cardValue.low;
    }
  }
  
  // Add aces, using 11 when possible
  let acesAsEleven = 0;
  for (let i = 0; i < aces; i++) {
    if (total + 11 + (aces - i - 1) <= 21) {
      total += 11;
      acesAsEleven++;
    } else {
      total += 1;
    }
  }
  
  return {
    value: total,
    isSoft: acesAsEleven > 0 && total <= 21
  };
}

/**
 * Create a new hand from cards
 */
export function createHand(cards: Card[] = []): Hand {
  const { value, isSoft } = calculateHandValue(cards);
  
  return {
    cards: [...cards],
    value,
    isSoft,
    isBusted: value > 21,
    isBlackjack: cards.length === 2 && value === 21,
    isFinished: false
  };
}

/**
 * Add a card to a hand and recalculate values
 */
export function addCardToHand(hand: Hand, card: Card): Hand {
  const newCards = [...hand.cards, card];
  const { value, isSoft } = calculateHandValue(newCards);
  
  return {
    cards: newCards,
    value,
    isSoft,
    isBusted: value > 21,
    isBlackjack: newCards.length === 2 && value === 21,
    isFinished: hand.isFinished || value > 21
  };
}

/**
 * Check if a player can double down
 */
export function canDoubleDown(hand: Hand): boolean {
  // Can only double on first two cards
  return hand.cards.length === 2 && !hand.isBusted && !hand.isFinished;
}

/**
 * Check if a player can hit
 */
export function canHit(hand: Hand): boolean {
  return !hand.isBusted && !hand.isFinished && hand.value < 21;
}

/**
 * Check if a player can stand
 */
export function canStand(hand: Hand): boolean {
  return !hand.isBusted && !hand.isFinished;
}

/**
 * Get valid actions for a hand
 */
export function getValidActions(hand: Hand): PlayerAction[] {
  const actions: PlayerAction[] = [];
  
  if (canHit(hand)) {
    actions.push(PlayerAction.HIT);
  }
  
  if (canStand(hand)) {
    actions.push(PlayerAction.STAND);
  }
  
  if (canDoubleDown(hand)) {
    actions.push(PlayerAction.DOUBLE);
  }
  
  return actions;
}

/**
 * Determine if dealer should hit according to standard blackjack rules
 */
export function shouldDealerHit(dealerHand: Hand): boolean {
  // Dealer hits on soft 17 and below, stands on hard 17 and above
  if (dealerHand.value < 17) {
    return true;
  }
  
  if (dealerHand.value === 17 && dealerHand.isSoft) {
    return true; // Hit on soft 17
  }
  
  return false;
}

/**
 * Compare player hand against dealer hand and determine result
 */
export function determineHandResult(playerHand: Hand, dealerHand: Hand): HandResult {
  // Player busted
  if (playerHand.isBusted) {
    return HandResult.BUST;
  }
  
  // Player has blackjack
  if (playerHand.isBlackjack) {
    if (dealerHand.isBlackjack) {
      return HandResult.PUSH;
    }
    return HandResult.BLACKJACK;
  }
  
  // Dealer busted
  if (dealerHand.isBusted) {
    return HandResult.WIN;
  }
  
  // Dealer has blackjack (and player doesn't)
  if (dealerHand.isBlackjack) {
    return HandResult.LOSE;
  }
  
  // Compare values
  if (playerHand.value > dealerHand.value) {
    return HandResult.WIN;
  } else if (playerHand.value < dealerHand.value) {
    return HandResult.LOSE;
  } else {
    return HandResult.PUSH;
  }
}

/**
 * Calculate payout based on hand result and bet amount
 */
export function calculatePayout(result: HandResult, betAmount: number): number {
  switch (result) {
    case HandResult.BLACKJACK:
      return Math.floor(betAmount * 1.5); // 3:2 payout for blackjack
    case HandResult.WIN:
      return betAmount; // 1:1 payout for regular win
    case HandResult.PUSH:
      return 0; // No win or loss
    case HandResult.LOSE:
    case HandResult.BUST:
      return -betAmount; // Lose the bet
    default:
      return 0;
  }
}

/**
 * Validate a bet amount
 */
export function validateBet(amount: number, balance: number, minBet: number, maxBet: number): {
  isValid: boolean;
  error?: string;
} {
  if (amount < 0) {
    return { isValid: false, error: 'Bet amount cannot be negative' };
  }
  
  if (amount === 0) {
    return { isValid: false, error: 'Bet amount must be greater than zero' };
  }
  
  if (amount < minBet) {
    return { isValid: false, error: `Minimum bet is ${minBet}` };
  }
  
  if (amount > maxBet) {
    return { isValid: false, error: `Maximum bet is ${maxBet}` };
  }
  
  if (amount > balance) {
    return { isValid: false, error: 'Insufficient balance' };
  }
  
  return { isValid: true };
}

/**
 * Check if a hand is a "soft" total (contains an ace counted as 11)
 */
export function isSoftHand(cards: Card[]): boolean {
  return calculateHandValue(cards).isSoft;
}

/**
 * Get the string representation of a hand for display
 */
export function getHandDisplayString(hand: Hand): string {
  if (hand.isBusted) {
    return `${hand.value} (BUST)`;
  }
  
  if (hand.isBlackjack) {
    return `${hand.value} (BLACKJACK)`;
  }
  
  if (hand.isSoft && hand.value !== 21) {
    return `${hand.value} (SOFT)`;
  }
  
  return hand.value.toString();
}

/**
 * Basic strategy recommendation (for educational purposes)
 */
export function getBasicStrategyAction(playerHand: Hand, dealerUpCard: Card): PlayerAction {
  const playerValue = playerHand.value;
  const dealerValue = getCardValue(dealerUpCard.rank).low;
  
  // This is a simplified basic strategy - a full implementation would be much more complex
  if (playerHand.isSoft) {
    // Soft hands
    if (playerValue >= 19) return PlayerAction.STAND;
    if (playerValue === 18 && dealerValue >= 9) return PlayerAction.HIT;
    if (playerValue === 18) return PlayerAction.STAND;
    return PlayerAction.HIT;
  } else {
    // Hard hands
    if (playerValue >= 17) return PlayerAction.STAND;
    if (playerValue >= 13 && dealerValue <= 6) return PlayerAction.STAND;
    if (playerValue === 12 && dealerValue >= 4 && dealerValue <= 6) return PlayerAction.STAND;
    if (playerValue === 11 && canDoubleDown(playerHand)) return PlayerAction.DOUBLE;
    if (playerValue === 10 && dealerValue <= 9 && canDoubleDown(playerHand)) return PlayerAction.DOUBLE;
    if (playerValue === 9 && dealerValue >= 3 && dealerValue <= 6 && canDoubleDown(playerHand)) return PlayerAction.DOUBLE;
    return PlayerAction.HIT;
  }
}
