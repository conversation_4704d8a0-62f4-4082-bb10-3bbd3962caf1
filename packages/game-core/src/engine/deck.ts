import { Card, Suit, Rank, createCard, getAllCards } from '../types/card.js';
import { SeededRNG, shuffleArray } from '../utils/rng.js';

/**
 * Represents a deck of cards with shuffling and dealing capabilities
 */
export class Deck {
  private cards: Card[];
  private originalCards: Card[];
  private dealtCards: Card[];

  constructor(numDecks: number = 6) {
    this.originalCards = this.createMultipleDecks(numDecks);
    this.cards = [...this.originalCards];
    this.dealtCards = [];
  }

  /**
   * Create multiple standard 52-card decks
   */
  private createMultipleDecks(numDecks: number): Card[] {
    const allCards: Card[] = [];
    for (let i = 0; i < numDecks; i++) {
      allCards.push(...getAllCards());
    }
    return allCards;
  }

  /**
   * Shuffle the deck using a seeded RNG for provably fair shuffling
   */
  shuffle(rng: SeededRNG): void {
    this.cards = shuffleArray(this.originalCards, rng);
    this.dealtCards = [];
  }

  /**
   * Deal a single card from the top of the deck
   */
  dealCard(faceUp: boolean = true): Card | null {
    if (this.cards.length === 0) {
      return null;
    }

    const card = this.cards.pop()!;
    card.faceUp = faceUp;
    this.dealtCards.push(card);
    return card;
  }

  /**
   * Deal multiple cards
   */
  dealCards(count: number, faceUp: boolean = true): Card[] {
    const cards: Card[] = [];
    for (let i = 0; i < count; i++) {
      const card = this.dealCard(faceUp);
      if (card) {
        cards.push(card);
      } else {
        break; // No more cards available
      }
    }
    return cards;
  }

  /**
   * Get the number of cards remaining in the deck
   */
  getRemainingCount(): number {
    return this.cards.length;
  }

  /**
   * Get the number of cards that have been dealt
   */
  getDealtCount(): number {
    return this.dealtCards.length;
  }

  /**
   * Check if the deck needs to be reshuffled (less than 25% remaining)
   */
  needsReshuffle(): boolean {
    const totalCards = this.originalCards.length;
    const remainingCards = this.cards.length;
    return remainingCards < (totalCards * 0.25);
  }

  /**
   * Reset the deck to its original state (before shuffling)
   */
  reset(): void {
    this.cards = [...this.originalCards];
    this.dealtCards = [];
  }

  /**
   * Get a copy of all cards that have been dealt (for verification)
   */
  getDealtCards(): Card[] {
    return [...this.dealtCards];
  }

  /**
   * Get a copy of the current deck state (for verification)
   */
  getCurrentDeck(): Card[] {
    return [...this.cards];
  }

  /**
   * Verify that the deck was shuffled correctly using the given RNG seed
   */
  verifyShuffleIntegrity(rng: SeededRNG): boolean {
    // Create a fresh deck and shuffle it with the same RNG
    const testDeck = new Deck(this.originalCards.length / 52);
    testDeck.shuffle(rng);
    
    // Compare the shuffled order
    const currentOrder = [...this.cards, ...this.dealtCards];
    const expectedOrder = [...testDeck.cards, ...testDeck.dealtCards];
    
    if (currentOrder.length !== expectedOrder.length) {
      return false;
    }

    for (let i = 0; i < currentOrder.length; i++) {
      const current = currentOrder[i];
      const expected = expectedOrder[i];
      
      if (current.suit !== expected.suit || current.rank !== expected.rank) {
        return false;
      }
    }

    return true;
  }

  /**
   * Create a deck from a specific card order (for testing/verification)
   */
  static fromCardOrder(cards: Card[]): Deck {
    const deck = new Deck(0); // Empty deck
    deck.originalCards = [...cards];
    deck.cards = [...cards];
    deck.dealtCards = [];
    return deck;
  }

  /**
   * Get the penetration percentage (how much of the deck has been dealt)
   */
  getPenetration(): number {
    const totalCards = this.originalCards.length;
    const dealtCards = this.dealtCards.length;
    return (dealtCards / totalCards) * 100;
  }

  /**
   * Peek at the next card without dealing it (for testing purposes)
   */
  peekNextCard(): Card | null {
    if (this.cards.length === 0) {
      return null;
    }
    return { ...this.cards[this.cards.length - 1] };
  }

  /**
   * Get deck statistics
   */
  getStats(): DeckStats {
    return {
      totalCards: this.originalCards.length,
      remainingCards: this.cards.length,
      dealtCards: this.dealtCards.length,
      penetration: this.getPenetration(),
      needsReshuffle: this.needsReshuffle()
    };
  }
}

/**
 * Deck statistics interface
 */
export interface DeckStats {
  totalCards: number;
  remainingCards: number;
  dealtCards: number;
  penetration: number;
  needsReshuffle: boolean;
}

/**
 * Create a standard 6-deck shoe for blackjack
 */
export function createBlackjackShoe(): Deck {
  return new Deck(6);
}

/**
 * Create a single deck for testing
 */
export function createSingleDeck(): Deck {
  return new Deck(1);
}
