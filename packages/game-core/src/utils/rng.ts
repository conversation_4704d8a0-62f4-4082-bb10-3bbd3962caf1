/**
 * Provably fair RNG utilities for deterministic shuffling
 * Uses server seed + round nonce for reproducible randomness
 */

/**
 * Simple hash function for converting string to number
 * This is a basic implementation - in production you might want to use a more robust hash
 */
function simpleHash(str: string): number {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash);
}

/**
 * Seeded random number generator using Linear Congruential Generator (LCG)
 * This provides deterministic pseudo-random numbers from a seed
 */
export class SeededRNG {
  private seed: number;
  private current: number;

  constructor(seed: string | number) {
    if (typeof seed === 'string') {
      this.seed = simpleHash(seed);
    } else {
      this.seed = seed;
    }
    this.current = this.seed;
  }

  /**
   * Generate next random number between 0 and 1
   */
  next(): number {
    // LCG parameters (from Numerical Recipes)
    const a = 1664525;
    const c = 1013904223;
    const m = Math.pow(2, 32);
    
    this.current = (a * this.current + c) % m;
    return this.current / m;
  }

  /**
   * Generate random integer between min (inclusive) and max (exclusive)
   */
  nextInt(min: number, max: number): number {
    return Math.floor(this.next() * (max - min)) + min;
  }

  /**
   * Reset the RNG to its initial seed
   */
  reset(): void {
    this.current = this.seed;
  }
}

/**
 * Create a deterministic seed from server seed, round nonce, table ID, and date
 */
export function createDeterministicSeed(
  serverSeed: string,
  roundNonce: number,
  tableId: string,
  date: string
): string {
  return `${serverSeed}|${roundNonce}|${tableId}|${date}`;
}

/**
 * Create SHA-256 hash of a seed (for revealing hash before round)
 * Note: This is a simple implementation. In production, use a proper crypto library
 */
export function hashSeed(seed: string): string {
  // This is a placeholder - in a real implementation, you'd use crypto.subtle.digest
  // or a proper crypto library like crypto-js
  let hash = 0;
  for (let i = 0; i < seed.length; i++) {
    const char = seed.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash;
  }
  return Math.abs(hash).toString(16).padStart(8, '0');
}

/**
 * Fisher-Yates shuffle algorithm using seeded RNG
 * This ensures the same seed always produces the same shuffle
 */
export function shuffleArray<T>(array: T[], rng: SeededRNG): T[] {
  const shuffled = [...array];
  
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = rng.nextInt(0, i + 1);
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  
  return shuffled;
}

/**
 * Verify that a shuffle was performed correctly using the given seed
 */
export function verifyShuffleIntegrity<T>(
  originalArray: T[],
  shuffledArray: T[],
  seed: string
): boolean {
  if (originalArray.length !== shuffledArray.length) {
    return false;
  }

  // Check that all elements are present
  const originalSet = new Set(originalArray.map(item => JSON.stringify(item)));
  const shuffledSet = new Set(shuffledArray.map(item => JSON.stringify(item)));
  
  if (originalSet.size !== shuffledSet.size) {
    return false;
  }

  for (const item of originalSet) {
    if (!shuffledSet.has(item)) {
      return false;
    }
  }

  // Verify the shuffle was performed with the given seed
  const rng = new SeededRNG(seed);
  const expectedShuffle = shuffleArray(originalArray, rng);
  
  return JSON.stringify(expectedShuffle) === JSON.stringify(shuffledArray);
}

/**
 * Generate a cryptographically secure random server seed
 * In a real implementation, this would use crypto.getRandomValues() or similar
 */
export function generateServerSeed(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 32; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * Provably fair verification data
 */
export interface FairnessProof {
  serverSeed: string;
  serverSeedHash: string;
  roundNonce: number;
  tableId: string;
  date: string;
  deterministicSeed: string;
}

/**
 * Create fairness proof for a round
 */
export function createFairnessProof(
  serverSeed: string,
  roundNonce: number,
  tableId: string,
  date: string
): FairnessProof {
  const deterministicSeed = createDeterministicSeed(serverSeed, roundNonce, tableId, date);
  const serverSeedHash = hashSeed(deterministicSeed);
  
  return {
    serverSeed,
    serverSeedHash,
    roundNonce,
    tableId,
    date,
    deterministicSeed
  };
}
