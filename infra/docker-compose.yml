services:
  cockroach:
    image: cockroachdb/cockroach:latest-v25.3
    command: start-single-node --insecure --http-addr 0.0.0.0:8080
    ports: ["26257:26257","8080:8080"]
    volumes: ["crdb:/cockroach/cockroach-data"]

  nakama:
    image: registry.heroiclabs.com/heroiclabs/nakama:3.22.0
    depends_on: [cockroach]
    entrypoint:
      - "/bin/sh"
      - "-ecx"
      - >
        /nakama/nakama migrate up --database.address <EMAIL>:26257 &&
        exec /nakama/nakama --name nakama1 --database.address <EMAIL>:26257 --logger.level DEBUG --session.token_expiry_sec 7200 --metrics.prometheus_port 9100
    restart: "no"
    volumes:
      - ../server/nakama.yml:/nakama/data/nakama.yml:ro
      - ../server/src:/nakama/data
    expose:
      - "7349"
      - "7350"
      - "7351"
      - "9100"
    ports:
      - "7349:7349"
      - "7350:7350"
      - "7351:7351"

volumes: { crdb: {} }
