{"name": "@blackjack/mcp-server", "version": "0.0.1", "type": "module", "description": "MCP server for Blackjack multiplayer game", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "tsc --watch", "start": "node dist/index.js", "typecheck": "tsc --noEmit"}, "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0", "ws": "^8.18.0", "express": "^4.19.0", "cors": "^2.8.5"}, "devDependencies": {"@types/node": "^20.0.0", "@types/ws": "^8.5.0", "@types/express": "^4.17.0", "@types/cors": "^2.8.0", "typescript": "^5.5.0"}}