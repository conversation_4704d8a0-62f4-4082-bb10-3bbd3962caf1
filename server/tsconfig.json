{"extends": "../tsconfig.base.json", "compilerOptions": {"outDir": "./dist", "rootDir": "./src", "target": "ES2020", "module": "ES2020", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}