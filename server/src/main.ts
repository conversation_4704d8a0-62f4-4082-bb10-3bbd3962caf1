/**
 * Main Nakama server entry point
 * Registers the blackjack match handler and other server functions
 */

import {
  matchInit,
  matchJoinAttempt,
  matchJoin,
  matchLoop,
  matchLeave,
  matchTerminate
} from './modules/blackjack-match.js';

import { WALLET_CONSTANTS, createInitialGrantMetadata } from '@blackjack/game-core';

/**
 * Initialize the Nakama server
 */
function InitModule(ctx: nkruntime.Context, logger: nkruntime.Logger, nk: nkruntime.Nakama, initializer: nkruntime.Initializer) {
  logger.info('Initializing Blackjack Multiplayer server');
  
  // Register the blackjack match handler
  initializer.registerMatch('blackjack', {
    matchInit,
    matchJoinAttempt,
    matchJoin,
    matchLoop,
    matchLeave,
    matchTerminate
  });
  
  // Register RPC functions
  initializer.registerRpc('create_table', createTable);
  initializer.registerRpc('join_table', joinTable);
  initializer.registerRpc('get_leaderboard', getLeaderboard);
  initializer.registerRpc('get_player_stats', getPlayerStats);
  
  // Register hooks
  initializer.registerAfterAuthenticateDevice(afterAuthenticateDevice);
  initializer.registerAfterAuthenticateEmail(afterAuthenticateEmail);
  initializer.registerAfterAuthenticateCustom(afterAuthenticateCustom);
  
  logger.info('Blackjack server initialized successfully');
}

/**
 * Create a new blackjack table
 */
function createTable(ctx: nkruntime.Context, logger: nkruntime.Logger, nk: nkruntime.Nakama, payload: string): string {
  const request = JSON.parse(payload) as {
    tableName?: string;
    minBet?: number;
    maxBet?: number;
  };
  
  const params = {
    tableName: request.tableName || `Table ${Date.now()}`,
    minBet: (request.minBet || WALLET_CONSTANTS.MIN_BET).toString(),
    maxBet: (request.maxBet || 10000).toString()
  };
  
  const matchId = nk.matchCreate('blackjack', params);
  
  logger.info(`Created new blackjack table: ${matchId}`);
  
  return JSON.stringify({
    success: true,
    matchId,
    tableName: params.tableName
  });
}

/**
 * Join a blackjack table
 */
function joinTable(ctx: nkruntime.Context, logger: nkruntime.Logger, nk: nkruntime.Nakama, payload: string): string {
  const request = JSON.parse(payload) as {
    tableId?: string;
  };
  
  try {
    let matchId = request.tableId;
    
    if (!matchId) {
      // Auto-match: find an available table
      const matches = nk.matchList(10, true, 'blackjack', 1, 7); // Tables with 1-7 players
      if (matches.length > 0) {
        matchId = matches[0].matchId;
      } else {
        // Create a new table
        matchId = nk.matchCreate('blackjack', {
          tableName: `Auto Table ${Date.now()}`,
          minBet: WALLET_CONSTANTS.MIN_BET.toString(),
          maxBet: '10000'
        });
      }
    }
    
    return JSON.stringify({
      success: true,
      matchId
    });
  } catch (error) {
    logger.error(`Error joining table: ${error}`);
    return JSON.stringify({
      success: false,
      error: 'Failed to join table'
    });
  }
}

/**
 * Get leaderboard data
 */
function getLeaderboard(ctx: nkruntime.Context, logger: nkruntime.Logger, nk: nkruntime.Nakama, payload: string): string {
  const request = JSON.parse(payload) as {
    limit?: number;
    period?: 'daily' | 'weekly' | 'monthly' | 'alltime';
  };
  
  const limit = Math.min(request.limit || 10, 100);
  const leaderboardId = `net_winnings_${request.period || 'daily'}`;
  
  try {
    const records = nk.leaderboardRecordsList(leaderboardId, [], limit);
    
    const leaderboard = records.records?.map((record, index) => ({
      rank: index + 1,
      userId: record.ownerId,
      displayName: record.username || `Player ${record.ownerId.substring(0, 8)}`,
      netWinnings: record.score,
      handsPlayed: record.metadata?.handsPlayed || 0
    })) || [];
    
    return JSON.stringify({
      success: true,
      leaderboard,
      period: request.period || 'daily'
    });
  } catch (error) {
    logger.error(`Error getting leaderboard: ${error}`);
    return JSON.stringify({
      success: false,
      error: 'Failed to get leaderboard'
    });
  }
}

/**
 * Get player statistics
 */
function getPlayerStats(ctx: nkruntime.Context, logger: nkruntime.Logger, nk: nkruntime.Nakama, payload: string): string {
  try {
    const userId = ctx.userId;
    
    // Get player stats from storage
    const objects = nk.storageRead([{
      collection: 'stats',
      key: `player:${userId}`,
      userId: userId
    }]);
    
    let stats = {
      handsPlayed: 0,
      handsWon: 0,
      totalWagered: 0,
      netWinnings: 0,
      blackjacksHit: 0,
      biggestWin: 0,
      currentWinStreak: 0,
      bestWinStreak: 0,
      averageBet: 0
    };
    
    if (objects.length > 0 && objects[0].value) {
      stats = JSON.parse(objects[0].value);
    }
    
    return JSON.stringify({
      success: true,
      stats
    });
  } catch (error) {
    logger.error(`Error getting player stats: ${error}`);
    return JSON.stringify({
      success: false,
      error: 'Failed to get player stats'
    });
  }
}

/**
 * After device authentication hook
 */
function afterAuthenticateDevice(ctx: nkruntime.Context, logger: nkruntime.Logger, nk: nkruntime.Nakama, data: nkruntime.AuthenticateDeviceRequest, result: nkruntime.AuthResult): void {
  if (result.created) {
    grantInitialBalance(ctx, logger, nk, result.account!.user.id);
  }
}

/**
 * After email authentication hook
 */
function afterAuthenticateEmail(ctx: nkruntime.Context, logger: nkruntime.Logger, nk: nkruntime.Nakama, data: nkruntime.AuthenticateEmailRequest, result: nkruntime.AuthResult): void {
  if (result.created) {
    grantInitialBalance(ctx, logger, nk, result.account!.user.id);
  }
}

/**
 * After custom authentication hook
 */
function afterAuthenticateCustom(ctx: nkruntime.Context, logger: nkruntime.Logger, nk: nkruntime.Nakama, data: nkruntime.AuthenticateCustomRequest, result: nkruntime.AuthResult): void {
  if (result.created) {
    grantInitialBalance(ctx, logger, nk, result.account!.user.id);
  }
}

/**
 * Grant initial balance to new players
 */
function grantInitialBalance(ctx: nkruntime.Context, logger: nkruntime.Logger, nk: nkruntime.Nakama, userId: string): void {
  try {
    const metadata = createInitialGrantMetadata(userId);
    
    nk.walletUpdate(userId, {
      [WALLET_CONSTANTS.CHIPS_WALLET_KEY]: WALLET_CONSTANTS.INITIAL_GRANT_AMOUNT
    }, metadata);
    
    logger.info(`Granted initial balance of ${WALLET_CONSTANTS.INITIAL_GRANT_AMOUNT} chips to new player ${userId}`);
  } catch (error) {
    logger.error(`Error granting initial balance to ${userId}: ${error}`);
  }
}

// Export the initialization function
export { InitModule };
