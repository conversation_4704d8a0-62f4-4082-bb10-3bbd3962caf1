/**
 * Blackjack Multiplayer Match Handler for Nakama
 * Implements all game phases and authoritative game logic
 */

import {
  GamePhase,
  PlayerAction,
  TableState,
  PlayerState,
  DealerState,
  RoundR<PERSON><PERSON>s,
  PlayerResult,
  HandResult,
  PHASE_TIMERS,
  createHand,
  addCardToHand,
  shouldDealerHit,
  determineHandResult,
  calculatePayout,
  validateBet
} from '@blackjack/game-core';

import {
  ClientOpcode,
  ServerOpcode,
  ClientMessage,
  ServerMessage,
  ErrorCode,
  createServerMessage
} from '@blackjack/game-core';

import {
  Deck,
  createBlackjackShoe
} from '@blackjack/game-core';

import {
  SeededRNG,
  createDeterministicSeed,
  hashSeed,
  generateServerSeed,
  createFairnessProof
} from '@blackjack/game-core';

import {
  WALLET_CONSTANTS,
  createBetPayoutMetadata,
  createFaucetMetadata,
  createInitialGrantMetadata
} from '@blackjack/game-core';

/**
 * Match state interface
 */
interface MatchState {
  tableState: TableState;
  deck: Deck;
  serverSeed: string;
  roundNonce: number;
  phaseTimer?: nkruntime.Timer;
  playerTimers: Map<string, nkruntime.Timer>;
}

/**
 * Maximum players per table
 */
const MAX_PLAYERS = 8;

/**
 * Minimum players to start a round
 */
const MIN_PLAYERS = 1;

/**
 * Match initialization
 */
function matchInit(ctx: nkruntime.Context, logger: nkruntime.Logger, nk: nkruntime.Nakama, params: {[key: string]: string}): {state: nkruntime.MatchState, tickRate: number, label: string} {
  logger.info('Initializing blackjack match');
  
  const tableId = ctx.matchId;
  const tableName = params.tableName || `Table ${tableId.substring(0, 8)}`;
  const minBet = parseInt(params.minBet) || WALLET_CONSTANTS.MIN_BET;
  const maxBet = parseInt(params.maxBet) || 10000;
  
  const initialState: MatchState = {
    tableState: {
      tableId,
      phase: GamePhase.BETTING,
      roundId: generateRoundId(),
      players: [],
      dealer: {
        hand: createHand(),
        hasRevealed: false
      },
      timeLeftMs: PHASE_TIMERS[GamePhase.BETTING],
      shoeDepth: 0,
      minBet,
      maxBet,
      tableName,
      spectatorCount: 0
    },
    deck: createBlackjackShoe(),
    serverSeed: generateServerSeed(),
    roundNonce: 1,
    playerTimers: new Map()
  };
  
  // Shuffle the deck
  const deterministicSeed = createDeterministicSeed(
    initialState.serverSeed,
    initialState.roundNonce,
    tableId,
    new Date().toISOString().split('T')[0]
  );
  const rng = new SeededRNG(deterministicSeed);
  initialState.deck.shuffle(rng);
  initialState.tableState.shoeDepth = initialState.deck.getRemainingCount();
  
  return {
    state: initialState,
    tickRate: 1, // 1 tick per second
    label: tableName
  };
}

/**
 * Player join attempt validation
 */
function matchJoinAttempt(ctx: nkruntime.Context, logger: nkruntime.Logger, nk: nkruntime.Nakama, dispatcher: nkruntime.MatchDispatcher, tick: number, state: nkruntime.MatchState, presence: nkruntime.Presence, metadata: {[key: string]: any}): {state: nkruntime.MatchState, accept: boolean, rejectMessage?: string} | null {
  const matchState = state as MatchState;
  
  // Check if table is full
  if (matchState.tableState.players.length >= MAX_PLAYERS) {
    return {
      state: matchState,
      accept: false,
      rejectMessage: 'Table is full'
    };
  }
  
  // Check if player is already in the match
  const existingPlayer = matchState.tableState.players.find(p => p.userId === presence.userId);
  if (existingPlayer) {
    return {
      state: matchState,
      accept: false,
      rejectMessage: 'Already in this table'
    };
  }
  
  return {
    state: matchState,
    accept: true
  };
}

/**
 * Player successfully joined
 */
function matchJoin(ctx: nkruntime.Context, logger: nkruntime.Logger, nk: nkruntime.Nakama, dispatcher: nkruntime.MatchDispatcher, tick: number, state: nkruntime.MatchState, presences: nkruntime.Presence[]): {state: nkruntime.MatchState} | null {
  const matchState = state as MatchState;
  
  for (const presence of presences) {
    // Get player profile and wallet
    const account = nk.accountGetId(presence.userId);
    const wallet = nk.walletRead(presence.userId);
    
    let balance = 0;
    if (wallet[WALLET_CONSTANTS.CHIPS_WALLET_KEY]) {
      balance = wallet[WALLET_CONSTANTS.CHIPS_WALLET_KEY];
    } else {
      // New player - grant initial balance
      const metadata = createInitialGrantMetadata(presence.userId);
      nk.walletUpdate(presence.userId, {
        [WALLET_CONSTANTS.CHIPS_WALLET_KEY]: WALLET_CONSTANTS.INITIAL_GRANT_AMOUNT
      }, metadata);
      balance = WALLET_CONSTANTS.INITIAL_GRANT_AMOUNT;
    }
    
    // Find available seat
    const seatIndex = findAvailableSeat(matchState.tableState.players);
    
    const newPlayer: PlayerState = {
      userId: presence.userId,
      displayName: account.user?.displayName || `Player ${presence.userId.substring(0, 8)}`,
      avatarUrl: account.user?.avatarUrl,
      hand: createHand(),
      currentBet: 0,
      balance,
      isActive: false,
      hasFinished: false,
      isConnected: true,
      seatIndex
    };
    
    matchState.tableState.players.push(newPlayer);
    
    logger.info(`Player ${newPlayer.displayName} joined table ${matchState.tableState.tableId}`);
  }
  
  // Send updated table state to all players
  broadcastTableState(dispatcher, matchState.tableState);
  
  return { state: matchState };
}

/**
 * Find available seat index
 */
function findAvailableSeat(players: PlayerState[]): number {
  const occupiedSeats = new Set(players.map(p => p.seatIndex));
  for (let i = 0; i < MAX_PLAYERS; i++) {
    if (!occupiedSeats.has(i)) {
      return i;
    }
  }
  return 0; // Fallback
}

/**
 * Generate a unique round ID
 */
function generateRoundId(): string {
  return `round_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
}

/**
 * Broadcast table state to all players
 */
function broadcastTableState(dispatcher: nkruntime.MatchDispatcher, tableState: TableState): void {
  const message = createServerMessage(ServerOpcode.TABLE_STATE, { tableState });
  dispatcher.broadcastMessage(message.opcode, JSON.stringify(message.payload));
}

/**
 * Send error message to specific player
 */
function sendError(dispatcher: nkruntime.MatchDispatcher, presence: nkruntime.Presence, code: ErrorCode, message: string): void {
  const errorMessage = createServerMessage(ServerOpcode.ERROR_MSG, { code, message });
  dispatcher.broadcastMessage(errorMessage.opcode, JSON.stringify(errorMessage.payload), [presence]);
}

/**
 * Main match loop - handles messages and timers
 */
function matchLoop(ctx: nkruntime.Context, logger: nkruntime.Logger, nk: nkruntime.Nakama, dispatcher: nkruntime.MatchDispatcher, tick: number, state: nkruntime.MatchState, messages: nkruntime.MatchMessage[]): {state: nkruntime.MatchState} | null {
  const matchState = state as MatchState;

  // Process incoming messages
  for (const message of messages) {
    try {
      const clientMessage: ClientMessage = JSON.parse(nk.binaryToString(message.data));
      handleClientMessage(ctx, logger, nk, dispatcher, matchState, message.sender, clientMessage);
    } catch (error) {
      logger.error(`Error processing message from ${message.sender.userId}: ${error}`);
      sendError(dispatcher, message.sender, ErrorCode.INVALID_REQUEST, 'Invalid message format');
    }
  }

  // Update timers
  if (matchState.tableState.timeLeftMs > 0) {
    matchState.tableState.timeLeftMs -= 1000; // Decrease by 1 second

    // Check if phase timer expired
    if (matchState.tableState.timeLeftMs <= 0) {
      handlePhaseTimeout(ctx, logger, nk, dispatcher, matchState);
    }
  }

  return { state: matchState };
}

/**
 * Handle client messages
 */
function handleClientMessage(
  ctx: nkruntime.Context,
  logger: nkruntime.Logger,
  nk: nkruntime.Nakama,
  dispatcher: nkruntime.MatchDispatcher,
  matchState: MatchState,
  sender: nkruntime.Presence,
  message: ClientMessage
): void {
  const player = matchState.tableState.players.find(p => p.userId === sender.userId);
  if (!player) {
    sendError(dispatcher, sender, ErrorCode.INVALID_REQUEST, 'Player not found in table');
    return;
  }

  switch (message.opcode) {
    case ClientOpcode.PLACE_BET:
      handlePlaceBet(ctx, logger, nk, dispatcher, matchState, player, message.payload);
      break;
    case ClientOpcode.ACTION_HIT:
      handlePlayerAction(ctx, logger, nk, dispatcher, matchState, player, PlayerAction.HIT);
      break;
    case ClientOpcode.ACTION_STAND:
      handlePlayerAction(ctx, logger, nk, dispatcher, matchState, player, PlayerAction.STAND);
      break;
    case ClientOpcode.ACTION_DOUBLE:
      handlePlayerAction(ctx, logger, nk, dispatcher, matchState, player, PlayerAction.DOUBLE);
      break;
    case ClientOpcode.LEAVE_TABLE:
      handleLeaveTable(ctx, logger, nk, dispatcher, matchState, player);
      break;
    default:
      sendError(dispatcher, sender, ErrorCode.INVALID_REQUEST, 'Unknown message type');
  }
}

/**
 * Handle phase timeout
 */
function handlePhaseTimeout(
  ctx: nkruntime.Context,
  logger: nkruntime.Logger,
  nk: nkruntime.Nakama,
  dispatcher: nkruntime.MatchDispatcher,
  matchState: MatchState
): void {
  switch (matchState.tableState.phase) {
    case GamePhase.BETTING:
      // Start dealing if at least one player has bet
      const playersWithBets = matchState.tableState.players.filter(p => p.currentBet > 0);
      if (playersWithBets.length > 0) {
        startDealPhase(ctx, logger, nk, dispatcher, matchState);
      } else {
        // No bets placed, restart betting phase
        restartBettingPhase(matchState);
        broadcastTableState(dispatcher, matchState.tableState);
      }
      break;
    case GamePhase.PLAYER_TURNS:
      // Auto-stand for current player
      const activePlayer = matchState.tableState.players.find(p => p.isActive);
      if (activePlayer) {
        handlePlayerAction(ctx, logger, nk, dispatcher, matchState, activePlayer, PlayerAction.STAND);
      }
      break;
    case GamePhase.DEAL:
      startPlayerTurns(matchState);
      broadcastTableState(dispatcher, matchState.tableState);
      break;
    case GamePhase.DEALER_TURN:
      continueDealerTurn(ctx, logger, nk, dispatcher, matchState);
      break;
    case GamePhase.SETTLE:
      startRevealPhase(matchState);
      broadcastTableState(dispatcher, matchState.tableState);
      break;
    case GamePhase.REVEAL:
      restartBettingPhase(matchState);
      broadcastTableState(dispatcher, matchState.tableState);
      break;
  }
}

/**
 * Handle place bet action
 */
function handlePlaceBet(
  ctx: nkruntime.Context,
  logger: nkruntime.Logger,
  nk: nkruntime.Nakama,
  dispatcher: nkruntime.MatchDispatcher,
  matchState: MatchState,
  player: PlayerState,
  payload: { amount: number }
): void {
  // Check if betting is allowed
  if (matchState.tableState.phase !== GamePhase.BETTING) {
    sendError(dispatcher, { userId: player.userId } as nkruntime.Presence, ErrorCode.BETTING_CLOSED, 'Betting is closed');
    return;
  }

  let betAmount = payload.amount;

  // Handle all-in bet
  if (betAmount === -1) {
    betAmount = player.balance;
  }

  // Check for zero balance and trigger faucet
  if (player.balance === 0) {
    const faucetMetadata = createFaucetMetadata(player.userId, matchState.tableState.tableId, matchState.tableState.roundId);
    nk.walletUpdate(player.userId, {
      [WALLET_CONSTANTS.CHIPS_WALLET_KEY]: WALLET_CONSTANTS.FAUCET_AMOUNT
    }, faucetMetadata);
    player.balance = WALLET_CONSTANTS.FAUCET_AMOUNT;

    // Send wallet update to player
    const walletMessage = createServerMessage(ServerOpcode.WALLET_UPDATE, {
      chips: player.balance,
      metadata: faucetMetadata
    });
    dispatcher.broadcastMessage(walletMessage.opcode, JSON.stringify(walletMessage.payload), [{ userId: player.userId } as nkruntime.Presence]);
  }

  // Validate bet
  const validation = validateBet(betAmount, player.balance, matchState.tableState.minBet, matchState.tableState.maxBet);
  if (!validation.isValid) {
    sendError(dispatcher, { userId: player.userId } as nkruntime.Presence, ErrorCode.INVALID_BET_AMOUNT, validation.error!);
    return;
  }

  // Place the bet
  player.currentBet = betAmount;
  player.balance -= betAmount;

  // Update wallet
  nk.walletUpdate(player.userId, {
    [WALLET_CONSTANTS.CHIPS_WALLET_KEY]: -betAmount
  }, { type: 'bet_placed', tableId: matchState.tableState.tableId, roundId: matchState.tableState.roundId });

  logger.info(`Player ${player.displayName} placed bet of ${betAmount}`);

  // Broadcast updated table state
  broadcastTableState(dispatcher, matchState.tableState);
}

/**
 * Start the deal phase
 */
function startDealPhase(
  ctx: nkruntime.Context,
  logger: nkruntime.Logger,
  nk: nkruntime.Nakama,
  dispatcher: nkruntime.MatchDispatcher,
  matchState: MatchState
): void {
  matchState.tableState.phase = GamePhase.DEAL;
  matchState.tableState.timeLeftMs = PHASE_TIMERS[GamePhase.DEAL];

  // Send round started message with server seed hash
  const fairnessProof = createFairnessProof(
    matchState.serverSeed,
    matchState.roundNonce,
    matchState.tableState.tableId,
    new Date().toISOString().split('T')[0]
  );

  matchState.tableState.serverSeedHash = fairnessProof.serverSeedHash;

  const roundStartedMessage = createServerMessage(ServerOpcode.ROUND_STARTED, {
    roundId: matchState.tableState.roundId,
    serverSeedHash: fairnessProof.serverSeedHash,
    shoeDepth: matchState.deck.getRemainingCount()
  });
  dispatcher.broadcastMessage(roundStartedMessage.opcode, JSON.stringify(roundStartedMessage.payload));

  // Deal initial cards
  dealInitialCards(dispatcher, matchState);

  logger.info(`Started deal phase for round ${matchState.tableState.roundId}`);
}

/**
 * Deal initial cards (2 to each player, 2 to dealer with one face down)
 */
function dealInitialCards(dispatcher: nkruntime.MatchDispatcher, matchState: MatchState): void {
  const playersWithBets = matchState.tableState.players.filter(p => p.currentBet > 0);

  // Deal first card to each player
  for (const player of playersWithBets) {
    const card = matchState.deck.dealCard(true);
    if (card) {
      player.hand = addCardToHand(player.hand, card);

      const cardMessage = createServerMessage(ServerOpcode.CARD_DEALT, {
        to: 'player',
        playerId: player.userId,
        card,
        handValue: player.hand.value
      });
      dispatcher.broadcastMessage(cardMessage.opcode, JSON.stringify(cardMessage.payload));
    }
  }

  // Deal first card to dealer (face up)
  const dealerCard1 = matchState.deck.dealCard(true);
  if (dealerCard1) {
    matchState.tableState.dealer.hand = addCardToHand(matchState.tableState.dealer.hand, dealerCard1);

    const cardMessage = createServerMessage(ServerOpcode.CARD_DEALT, {
      to: 'dealer',
      card: dealerCard1,
      handValue: matchState.tableState.dealer.hand.value
    });
    dispatcher.broadcastMessage(cardMessage.opcode, JSON.stringify(cardMessage.payload));
  }

  // Deal second card to each player
  for (const player of playersWithBets) {
    const card = matchState.deck.dealCard(true);
    if (card) {
      player.hand = addCardToHand(player.hand, card);

      const cardMessage = createServerMessage(ServerOpcode.CARD_DEALT, {
        to: 'player',
        playerId: player.userId,
        card,
        handValue: player.hand.value
      });
      dispatcher.broadcastMessage(cardMessage.opcode, JSON.stringify(cardMessage.payload));
    }
  }

  // Deal second card to dealer (face down)
  const dealerCard2 = matchState.deck.dealCard(false);
  if (dealerCard2) {
    matchState.tableState.dealer.hand = addCardToHand(matchState.tableState.dealer.hand, dealerCard2);

    const cardMessage = createServerMessage(ServerOpcode.CARD_DEALT, {
      to: 'dealer',
      card: dealerCard2
    });
    dispatcher.broadcastMessage(cardMessage.opcode, JSON.stringify(cardMessage.payload));
  }

  matchState.tableState.shoeDepth = matchState.deck.getRemainingCount();
}

/**
 * Start player turns phase
 */
function startPlayerTurns(matchState: MatchState): void {
  matchState.tableState.phase = GamePhase.PLAYER_TURNS;

  const playersWithBets = matchState.tableState.players.filter(p => p.currentBet > 0);
  if (playersWithBets.length === 0) {
    // No players with bets, skip to reveal
    startRevealPhase(matchState);
    return;
  }

  // Find first player to act
  const firstPlayer = playersWithBets[0];
  firstPlayer.isActive = true;
  matchState.tableState.actingPlayerId = firstPlayer.userId;
  matchState.tableState.timeLeftMs = PHASE_TIMERS[GamePhase.PLAYER_TURNS];
}

/**
 * Handle player action (Hit, Stand, Double)
 */
function handlePlayerAction(
  ctx: nkruntime.Context,
  logger: nkruntime.Logger,
  nk: nkruntime.Nakama,
  dispatcher: nkruntime.MatchDispatcher,
  matchState: MatchState,
  player: PlayerState,
  action: PlayerAction
): void {
  // Validate it's the player's turn
  if (matchState.tableState.phase !== GamePhase.PLAYER_TURNS || !player.isActive) {
    sendError(dispatcher, { userId: player.userId } as nkruntime.Presence, ErrorCode.NOT_YOUR_TURN, 'Not your turn');
    return;
  }

  logger.info(`Player ${player.displayName} chose action: ${action}`);

  switch (action) {
    case PlayerAction.HIT:
      const hitCard = matchState.deck.dealCard(true);
      if (hitCard) {
        player.hand = addCardToHand(player.hand, hitCard);

        const cardMessage = createServerMessage(ServerOpcode.CARD_DEALT, {
          to: 'player',
          playerId: player.userId,
          card: hitCard,
          handValue: player.hand.value
        });
        dispatcher.broadcastMessage(cardMessage.opcode, JSON.stringify(cardMessage.payload));

        // Check if player busted or got 21
        if (player.hand.isBusted || player.hand.value === 21) {
          finishPlayerTurn(matchState, player);
        }
      }
      break;

    case PlayerAction.STAND:
      finishPlayerTurn(matchState, player);
      break;

    case PlayerAction.DOUBLE:
      // Validate double down is allowed
      if (player.hand.cards.length !== 2) {
        sendError(dispatcher, { userId: player.userId } as nkruntime.Presence, ErrorCode.ACTION_NOT_ALLOWED, 'Can only double on first two cards');
        return;
      }

      if (player.balance < player.currentBet) {
        sendError(dispatcher, { userId: player.userId } as nkruntime.Presence, ErrorCode.INSUFFICIENT_BALANCE, 'Insufficient balance to double');
        return;
      }

      // Double the bet
      player.balance -= player.currentBet;
      player.currentBet *= 2;

      // Update wallet
      nk.walletUpdate(player.userId, {
        [WALLET_CONSTANTS.CHIPS_WALLET_KEY]: -player.currentBet / 2
      }, { type: 'double_down', tableId: matchState.tableState.tableId, roundId: matchState.tableState.roundId });

      // Deal one card and finish turn
      const doubleCard = matchState.deck.dealCard(true);
      if (doubleCard) {
        player.hand = addCardToHand(player.hand, doubleCard);

        const cardMessage = createServerMessage(ServerOpcode.CARD_DEALT, {
          to: 'player',
          playerId: player.userId,
          card: doubleCard,
          handValue: player.hand.value
        });
        dispatcher.broadcastMessage(cardMessage.opcode, JSON.stringify(cardMessage.payload));
      }

      finishPlayerTurn(matchState, player);
      break;
  }

  matchState.tableState.shoeDepth = matchState.deck.getRemainingCount();
  broadcastTableState(dispatcher, matchState.tableState);
}

/**
 * Finish current player's turn and move to next player
 */
function finishPlayerTurn(matchState: MatchState, player: PlayerState): void {
  player.isActive = false;
  player.hasFinished = true;

  // Find next player
  const playersWithBets = matchState.tableState.players.filter(p => p.currentBet > 0 && !p.hasFinished);

  if (playersWithBets.length > 0) {
    // Move to next player
    const nextPlayer = playersWithBets[0];
    nextPlayer.isActive = true;
    matchState.tableState.actingPlayerId = nextPlayer.userId;
    matchState.tableState.timeLeftMs = PHASE_TIMERS[GamePhase.PLAYER_TURNS];
  } else {
    // All players finished, move to dealer turn
    matchState.tableState.actingPlayerId = undefined;
    startDealerTurn(matchState);
  }
}

/**
 * Start dealer turn
 */
function startDealerTurn(matchState: MatchState): void {
  matchState.tableState.phase = GamePhase.DEALER_TURN;
  matchState.tableState.timeLeftMs = PHASE_TIMERS[GamePhase.DEALER_TURN];

  // Reveal dealer's hole card
  if (matchState.tableState.dealer.hand.cards.length >= 2) {
    matchState.tableState.dealer.hand.cards[1].faceUp = true;
    matchState.tableState.dealer.hasRevealed = true;
  }
}

/**
 * Continue dealer turn (hit until 17 or higher)
 */
function continueDealerTurn(
  ctx: nkruntime.Context,
  logger: nkruntime.Logger,
  nk: nkruntime.Nakama,
  dispatcher: nkruntime.MatchDispatcher,
  matchState: MatchState
): void {
  if (shouldDealerHit(matchState.tableState.dealer.hand)) {
    const dealerCard = matchState.deck.dealCard(true);
    if (dealerCard) {
      matchState.tableState.dealer.hand = addCardToHand(matchState.tableState.dealer.hand, dealerCard);

      const cardMessage = createServerMessage(ServerOpcode.CARD_DEALT, {
        to: 'dealer',
        card: dealerCard,
        handValue: matchState.tableState.dealer.hand.value
      });
      dispatcher.broadcastMessage(cardMessage.opcode, JSON.stringify(cardMessage.payload));

      matchState.tableState.shoeDepth = matchState.deck.getRemainingCount();
      matchState.tableState.timeLeftMs = PHASE_TIMERS[GamePhase.DEALER_TURN];
    }
  } else {
    // Dealer finished, move to settle
    startSettlePhase(ctx, logger, nk, dispatcher, matchState);
  }

  broadcastTableState(dispatcher, matchState.tableState);
}

/**
 * Start settle phase - calculate results and payouts
 */
function startSettlePhase(
  ctx: nkruntime.Context,
  logger: nkruntime.Logger,
  nk: nkruntime.Nakama,
  dispatcher: nkruntime.MatchDispatcher,
  matchState: MatchState
): void {
  matchState.tableState.phase = GamePhase.SETTLE;
  matchState.tableState.timeLeftMs = PHASE_TIMERS[GamePhase.SETTLE];

  const results: PlayerResult[] = [];
  const playersWithBets = matchState.tableState.players.filter(p => p.currentBet > 0);

  for (const player of playersWithBets) {
    const handResult = determineHandResult(player.hand, matchState.tableState.dealer.hand);
    const payout = calculatePayout(handResult, player.currentBet);

    // Update player balance
    if (payout !== 0) {
      player.balance += player.currentBet + payout; // Return bet + winnings

      // Update wallet
      const metadata = createBetPayoutMetadata(
        player.userId,
        matchState.tableState.tableId,
        matchState.tableState.roundId,
        player.currentBet,
        payout,
        handResult === HandResult.BLACKJACK ? 'blackjack' :
        handResult === HandResult.WIN ? 'win' :
        handResult === HandResult.LOSE || handResult === HandResult.BUST ? 'lose' : 'push'
      );

      nk.walletUpdate(player.userId, {
        [WALLET_CONSTANTS.CHIPS_WALLET_KEY]: player.currentBet + payout
      }, metadata);

      // Send wallet update
      const walletMessage = createServerMessage(ServerOpcode.WALLET_UPDATE, {
        chips: player.balance,
        metadata
      });
      dispatcher.broadcastMessage(walletMessage.opcode, JSON.stringify(walletMessage.payload), [{ userId: player.userId } as nkruntime.Presence]);
    } else if (handResult === HandResult.PUSH) {
      // Return bet on push
      player.balance += player.currentBet;

      const metadata = createBetPayoutMetadata(
        player.userId,
        matchState.tableState.tableId,
        matchState.tableState.roundId,
        player.currentBet,
        0,
        'push'
      );

      nk.walletUpdate(player.userId, {
        [WALLET_CONSTANTS.CHIPS_WALLET_KEY]: player.currentBet
      }, metadata);
    }

    results.push({
      userId: player.userId,
      handResult,
      betAmount: player.currentBet,
      payout,
      finalHandValue: player.hand.value
    });

    logger.info(`Player ${player.displayName}: ${handResult}, bet: ${player.currentBet}, payout: ${payout}`);
  }

  // Send round results
  const roundResults: RoundResults = {
    roundId: matchState.tableState.roundId,
    dealerFinalValue: matchState.tableState.dealer.hand.value,
    dealerBusted: matchState.tableState.dealer.hand.isBusted,
    playerResults: results,
    serverSeed: matchState.serverSeed,
    roundNonce: matchState.roundNonce
  };

  const resultMessage = createServerMessage(ServerOpcode.ROUND_RESULT, { results: roundResults });
  dispatcher.broadcastMessage(resultMessage.opcode, JSON.stringify(resultMessage.payload));

  broadcastTableState(dispatcher, matchState.tableState);
}

/**
 * Start reveal phase - show provably fair seed
 */
function startRevealPhase(matchState: MatchState): void {
  matchState.tableState.phase = GamePhase.REVEAL;
  matchState.tableState.timeLeftMs = PHASE_TIMERS[GamePhase.REVEAL];
}

/**
 * Restart betting phase for next round
 */
function restartBettingPhase(matchState: MatchState): void {
  // Reset for next round
  matchState.tableState.phase = GamePhase.BETTING;
  matchState.tableState.roundId = generateRoundId();
  matchState.tableState.timeLeftMs = PHASE_TIMERS[GamePhase.BETTING];
  matchState.tableState.actingPlayerId = undefined;
  matchState.tableState.serverSeedHash = undefined;

  // Reset player states
  for (const player of matchState.tableState.players) {
    player.hand = createHand();
    player.currentBet = 0;
    player.isActive = false;
    player.hasFinished = false;
  }

  // Reset dealer
  matchState.tableState.dealer = {
    hand: createHand(),
    hasRevealed: false
  };

  // Generate new server seed and increment nonce
  matchState.serverSeed = generateServerSeed();
  matchState.roundNonce++;

  // Check if deck needs reshuffling
  if (matchState.deck.needsReshuffle()) {
    const deterministicSeed = createDeterministicSeed(
      matchState.serverSeed,
      matchState.roundNonce,
      matchState.tableState.tableId,
      new Date().toISOString().split('T')[0]
    );
    const rng = new SeededRNG(deterministicSeed);
    matchState.deck.reset();
    matchState.deck.shuffle(rng);
  }

  matchState.tableState.shoeDepth = matchState.deck.getRemainingCount();
}

/**
 * Handle player leaving table
 */
function handleLeaveTable(
  ctx: nkruntime.Context,
  logger: nkruntime.Logger,
  nk: nkruntime.Nakama,
  dispatcher: nkruntime.MatchDispatcher,
  matchState: MatchState,
  player: PlayerState
): void {
  // Remove player from table
  const playerIndex = matchState.tableState.players.findIndex(p => p.userId === player.userId);
  if (playerIndex !== -1) {
    matchState.tableState.players.splice(playerIndex, 1);
    logger.info(`Player ${player.displayName} left table ${matchState.tableState.tableId}`);
    broadcastTableState(dispatcher, matchState.tableState);
  }
}

/**
 * Player disconnected
 */
function matchLeave(ctx: nkruntime.Context, logger: nkruntime.Logger, nk: nkruntime.Nakama, dispatcher: nkruntime.MatchDispatcher, tick: number, state: nkruntime.MatchState, presences: nkruntime.Presence[]): {state: nkruntime.MatchState} | null {
  const matchState = state as MatchState;

  for (const presence of presences) {
    const player = matchState.tableState.players.find(p => p.userId === presence.userId);
    if (player) {
      player.isConnected = false;

      // If it's their turn, auto-stand
      if (player.isActive && matchState.tableState.phase === GamePhase.PLAYER_TURNS) {
        handlePlayerAction(ctx, logger, nk, dispatcher, matchState, player, PlayerAction.STAND);
      }

      logger.info(`Player ${player.displayName} disconnected from table ${matchState.tableState.tableId}`);
    }
  }

  broadcastTableState(dispatcher, matchState.tableState);
  return { state: matchState };
}

/**
 * Match termination
 */
function matchTerminate(ctx: nkruntime.Context, logger: nkruntime.Logger, nk: nkruntime.Nakama, dispatcher: nkruntime.MatchDispatcher, tick: number, state: nkruntime.MatchState, graceSeconds: number): {state: nkruntime.MatchState} | null {
  logger.info(`Terminating match ${ctx.matchId}`);
  return null;
}

export {
  matchInit,
  matchJoinAttempt,
  matchJoin,
  matchLoop,
  matchLeave,
  matchTerminate
};
